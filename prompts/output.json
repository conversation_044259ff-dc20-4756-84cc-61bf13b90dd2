{"type": "object", "properties": {"reply": {"type": "array", "items": {"type": "object", "properties": {"character": {"type": "string"}, "delay": {"type": "integer", "description": "Delay in milliseconds before showing this message"}, "text": {"type": "string"}}, "required": ["character", "text", "delay"], "propertyOrdering": ["character", "text", "delay"]}}, "skills": {"type": "array", "items": {"type": "string"}}, "theme": {"type": "string"}}, "required": ["reply", "skills", "theme"], "propertyOrdering": ["reply", "skills", "theme"]}