import dotenv from 'dotenv';
import { AppConfig } from '../models/types';

// Load environment variables
dotenv.config();

export const config: AppConfig = {
  database: {
    url: process.env.DBOS_DATABASE_URL || 'postgresql://localhost:5432/forachat',
    client: 'knex',
  },
  llm: {
    apiKey: process.env.GEMINI_API_KEY || '',
    model: process.env.GEMINI_MODEL || 'gemini-2.5-flash',
    maxTokens: parseInt(process.env.GEMINI_MAX_TOKENS || '8192'),
  },
  port: parseInt(process.env.PORT || '3000'),
  environment: (process.env.NODE_ENV as 'development' | 'production' | 'test') || 'development',
};

export const validateConfig = (): void => {
  const requiredEnvVars = ['GEMINI_API_KEY', 'DBOS_DATABASE_URL'];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }
};

export default config;
