import { ChatResponse } from '../models/types';

// Abstract base class for LLM services
export abstract class LLMService {
  abstract generate(systemPrompt: string, userPrompt: string): Promise<ChatResponse>;
}

// Interface for LLM providers
export interface LLMProvider {
  name: string;
  generate(systemPrompt: string, userPrompt: string): Promise<ChatResponse>;
}

// Factory for creating LLM services
export class LLMServiceFactory {
  private static providers: Map<string, () => LLMService> = new Map();

  static register(name: string, factory: () => LLMService): void {
    this.providers.set(name, factory);
  }

  static create(providerName: string): LLMService {
    const factory = this.providers.get(providerName);
    if (!factory) {
      throw new Error(`Unknown LLM provider: ${providerName}`);
    }
    return factory();
  }

  static getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }
}
