import { REPLInterface } from './interfaces/REPLInterface';
import { ForaChatApp } from './ForaChatApp';

function parseArgs(): { sessionId?: string; serverUrl?: string; serverPort?: number; local?: boolean } {
    const args = process.argv.slice(2);
    const result: { sessionId?: string; serverUrl?: string; serverPort?: number; local?: boolean } = {};

    for (let i = 0; i < args.length; i++) {
        const arg = args[i];

        if (arg === '--session-id' && i + 1 < args.length) {
            result.sessionId = args[i + 1];
            i++; // Skip next argument as it's the value
        } else if (arg === '--server-url' && i + 1 < args.length) {
            result.serverUrl = args[i + 1];
            i++;
        } else if (arg === '--server-port' && i + 1 < args.length) {
            result.serverPort = parseInt(args[i + 1]);
            i++;
        } else if (arg === '--local') {
            result.local = true;
        } else if (arg === '--help' || arg === '-h') {
            console.log('ForaChat REPL');
            console.log('Usage: npm run repl -- [options]');
            console.log('');
            console.log('Note: Use -- to pass options to the REPL script');
            console.log('');
            console.log('Options:');
            console.log('  --session-id <id>     Resume a previous session');
            console.log('  --server-url <url>    Server URL (default: localhost)');
            console.log('  --server-port <port>  Server port (default: 3000)');
            console.log('  --local               Start the ForaChat server locally');
            console.log('  --help, -h            Show this help message');
            console.log('');
            console.log('Examples:');
            console.log('  npm run repl                    # Connect to existing server');
            console.log('  npm run repl -- --local         # Start server locally and connect');
            console.log('  npm run repl -- --session-id 123 # Resume specific session');
            process.exit(0);
        }
    }

    return result;
}

async function main() {
    try {
        const args = parseArgs();

        // Start the server if --local flag is provided
        if (args.local) {
            console.log('🚀 Starting ForaChat server locally...');
            const app = new ForaChatApp();
            await app.start();
            console.log('✅ ForaChat server started successfully');

            // Add a small delay to ensure server is fully ready
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        const repl = new REPLInterface(
            args.serverUrl || 'localhost',
            args.serverPort || 3000,
            args.sessionId
        );
        await repl.start();
    } catch (error) {
        console.error('❌ REPL failed to start:', error);
        process.exit(1);
    }
}

main();
