import readline from 'readline';
import http from 'http';
import os from 'os';
import { MessageInterface, ChatResponse, SessionCreateRequest } from '../models/types';


export class REPLInterface implements MessageInterface {
  private rl: readline.Interface;
  private serverUrl: string;
  private serverPort: number;
  private currentConversationId: number | null = null;
  private lastMessageId: number = 0;
  private pollingInterval: NodeJS.Timeout | null = null;
  private sessionId: string | null = null;
  private userIdentifier: string;

  constructor(serverUrl: string = 'localhost', serverPort: number = 3000, sessionId?: string) {
    this.serverUrl = serverUrl;
    this.serverPort = serverPort;
    this.sessionId = sessionId || null;
    this.userIdentifier = `${os.hostname()}_${os.userInfo().username}`;

    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: '> '
    });
  }

  async start(): Promise<void> {
    console.log('🚀 ForaChat REPL started!');
    console.log('Type your workplace questions and get advice from <PERSON><PERSON>, <PERSON>, and <PERSON>.');

    // Initialize or restore session
    await this.initializeSession();

    console.log('Type "exit" or "quit" to end the session.\n');

    this.rl.prompt();
    
    this.rl.on('line', async (line) => {
      const input = line.trim();
      
      if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
        this.rl.close();
        return;
      }
      
      if (input.length === 0) {
        this.rl.prompt();
        return;
      }

      try {
        await this.sendMessage(input);
      } catch (error) {
        console.error(`Error: ${(error as Error).message}`);
      }
      
      this.rl.prompt();
    });

    this.rl.on('close', () => {
      // Clean up polling
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
      }
      console.log('\n👋 Thanks for using ForaChat! Have a great day!');
      process.exit(0);
    });
  }

  private async initializeSession(): Promise<void> {
    try {
      if (this.sessionId) {
        // Try to restore existing session
        const sessionInfo = await this.getSessionInfo(this.sessionId);
        if (sessionInfo) {
          this.currentConversationId = sessionInfo.conversationId;
          console.log(`📋 Restored session: ${this.sessionId}`);
          if (sessionInfo.conversationId) {
            console.log(`💬 Continuing conversation: ${sessionInfo.conversationId}`);
            // Optionally show recent messages
            await this.showRecentMessages(sessionInfo.conversationId);
          }
        } else {
          console.log(`⚠️  Session ${this.sessionId} not found, creating new session`);
          this.sessionId = null;
        }
      }

      if (!this.sessionId) {
        // Create new session
        this.sessionId = await this.createSession();
        console.log(`🆕 Created new session: ${this.sessionId}`);
      }
    } catch (error) {
      console.error(`Error initializing session: ${(error as Error).message}`);
      console.log('Continuing without session management...');
    }
  }

  private async createSession(): Promise<string> {
    const sessionRequest: SessionCreateRequest = {
      userIdentifier: `repl_${this.userIdentifier}`,
      channel: 'repl',
      metadata: {
        hostname: os.hostname(),
        username: os.userInfo().username,
        platform: os.platform()
      }
    };

    const postData = JSON.stringify(sessionRequest);
    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: '/api/session',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            resolve(response.sessionId || response.id);
          } catch (error) {
            reject(error);
          }
        });
      });
      req.on('error', reject);
      req.write(postData);
      req.end();
    });
  }

  private async getSessionInfo(sessionId: string): Promise<any> {
    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: `/api/session/${sessionId}`,
      method: 'GET'
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });
        res.on('end', () => {
          try {
            if (res.statusCode === 404) {
              resolve(null);
            } else {
              const response = JSON.parse(data);
              resolve(response);
            }
          } catch (error) {
            reject(error);
          }
        });
      });
      req.on('error', reject);
      req.end();
    });
  }

  private async showRecentMessages(conversationId: number): Promise<void> {
    try {
      const options = {
        hostname: this.serverUrl,
        port: this.serverPort,
        path: `/conversation/${conversationId}`,
        method: 'GET'
      };

      const messages = await new Promise<any>((resolve, reject) => {
        const req = http.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });
          res.on('end', () => {
            try {
              const response = JSON.parse(data);
              resolve(response.messages || []);
            } catch (error) {
              reject(error);
            }
          });
        });
        req.on('error', reject);
        req.end();
      });

      if (messages.length > 0) {
        console.log('\n📜 Recent conversation history:');
        const recentMessages = messages.slice(-6); // Show last 6 messages
        recentMessages.forEach((msg: any) => {
          const timestamp = new Date(msg.created_at).toLocaleTimeString();
          console.log(`[${timestamp}] ${msg.character}: ${msg.text}`);
        });
        console.log(''); // Empty line after history
      }
    } catch (error) {
      console.log('Could not load conversation history');
    }
  }

  async sendMessage(message: string): Promise<void> {
    const postData = JSON.stringify({ text: message });

    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: '/chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', async () => {
          try {
            const response = JSON.parse(data);
            await this.displayResponse(response);
            resolve();
          } catch (error) {
            console.error('Failed to parse response:', error);
            console.error('Raw response:', data);
            reject(error);
          }
        });
      });

      req.on('error', (e) => {
        console.error(`Connection error: ${e.message}`);
        console.error('Make sure the ForaChat server is running on port 3000');
        reject(e);
      });

      req.write(postData);
      req.end();
    });
  }

  async receiveMessage(): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question('> ', (answer) => {
        resolve(answer.trim());
      });
    });
  }

  formatResponse(response: ChatResponse): string {
    let formatted = `\n--- ${response.theme || 'Chat Response'} ---\n`;

    response.reply.forEach((message) => {
      formatted += `${message.character}: ${message.text}\n`;
    });

    if (response.skills && response.skills.length > 0) {
      formatted += `\nSkills: ${response.skills.join(', ')}\n`;
    }

    return formatted;
  }

  private async displayResponse(response: any): Promise<void> {
    // Handle error responses
    if (response.error) {
      console.log(`❌ Error: ${response.error}`);
      if (response.details) {
        console.log(`Details: ${response.details}`);
      }
      return;
    }

    // Handle successful responses with reply array
    if (response.reply && Array.isArray(response.reply)) {
      // Display theme header
      console.log(`\n--- ${response.theme || 'Chat Response'} ---`);

      // Display messages with staggered delays and timestamps
      await this.displayStaggeredMessages(response.reply);

      // Display skills if present
      if (response.skills && response.skills.length > 0) {
        console.log(`\nSkills: ${response.skills.join(', ')}`);
      }

      console.log(''); // Empty line after response

      // Store conversation ID and start polling for delayed thoughts
      if (response.conversationId) {
        this.currentConversationId = response.conversationId;
        // Set initial lastMessageId to avoid re-showing initial messages
        this.setInitialLastMessageId();
        this.startPollingForDelayedThoughts();
      }
    } else {
      console.log('Unexpected response format:', JSON.stringify(response, null, 2));
    }
  }

  private async displayStaggeredMessages(messages: any[]): Promise<void> {
    let cumulativeDelay = 0;

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const delay = message.delay || 0;

      // Add the delay for this message to cumulative delay
      cumulativeDelay += delay;

      // Wait for the cumulative delay
      if (cumulativeDelay > 0) {
        await new Promise(resolve => setTimeout(resolve, cumulativeDelay));
      }

      // Display the message with timestamp
      const timestamp = new Date().toLocaleTimeString();
      console.log(`[${timestamp}] ${message.character}: ${message.text}`);

      // Reset cumulative delay for next message (each message's delay is independent)
      cumulativeDelay = 0;
    }
  }

  private async setInitialLastMessageId(): Promise<void> {
    if (!this.currentConversationId) return;

    try {
      const options = {
        hostname: this.serverUrl,
        port: this.serverPort,
        path: `/conversation/${this.currentConversationId}`,
        method: 'GET'
      };

      return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
          let data = '';

          res.on('data', (chunk) => {
            data += chunk;
          });

          res.on('end', () => {
            try {
              const response = JSON.parse(data);
              if (response.messages && Array.isArray(response.messages)) {
                // Set lastMessageId to the highest current message ID
                const messageIds = response.messages.map((msg: any) => msg.id);
                if (messageIds.length > 0) {
                  this.lastMessageId = Math.max(...messageIds);
                }
              }
              resolve();
            } catch (error) {
              reject(error);
            }
          });
        });

        req.on('error', (e) => {
          reject(e);
        });

        req.end();
      });
    } catch (error) {
      // Silently handle errors
    }
  }

  private startPollingForDelayedThoughts(): void {
    // Clear any existing polling
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }

    console.log('\n⏳ Listening for character follow-up thoughts...\n');

    // Poll every 10 seconds for delayed thoughts
    this.pollingInterval = setInterval(async () => {
      try {
        await this.checkForDelayedThoughts();
      } catch (error) {
        // Silently handle errors to avoid cluttering the REPL
      }
    }, 10000);

    // Stop polling after 10 minutes
    setTimeout(() => {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
        console.log('\n⏰ Stopped listening for character thoughts.\n');
      }
    }, 600000); // 10 minutes
  }

  private async checkForDelayedThoughts(): Promise<void> {
    if (!this.currentConversationId) return;

    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: `/conversation/${this.currentConversationId}`,
      method: 'GET'
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            if (response.messages && Array.isArray(response.messages)) {
              // Filter for new messages since last check
              const newMessages = response.messages.filter((msg: any) =>
                msg.id > this.lastMessageId && msg.character !== 'user'
              );

              if (newMessages.length > 0) {
                console.log('\n💭 Character follow-up thoughts:');
                newMessages.forEach((msg: any) => {
                  const timestamp = new Date(msg.created_at || new Date()).toLocaleTimeString();
                  console.log(`[${timestamp}] ${msg.character}: ${msg.text}`);
                });
                console.log(''); // Empty line for spacing

                // Update last message ID
                this.lastMessageId = Math.max(...newMessages.map((msg: any) => msg.id));
              }
            }
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });

      req.on('error', (e) => {
        reject(e);
      });

      req.end();
    });
  }

  // Utility methods for enhanced REPL experience
  setPrompt(prompt: string): void {
    this.rl.setPrompt(prompt);
  }

  displayHelp(): void {
    console.log(`
📚 ForaChat Help:
- Ask questions about workplace skills like communication, leadership, teamwork
- Examples:
  • "How can I give better feedback to my team?"
  • "I'm having conflict with a coworker"
  • "How do I run more effective meetings?"
- Type "exit" or "quit" to end the session
- The team (Fora, Jan, Lou) will provide practical advice!
    `);
  }

  close(): void {
    this.rl.close();
  }
}
