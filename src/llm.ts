import { GeminiLLMService } from './services/GeminiLLMService';

// Legacy LLM class for backward compatibility
// New code should use GeminiLLMService or other LLM services directly
export class LLM {
    private static service = new GeminiLLMService();

    static async generate(systemPrompt: string, userPrompt: string): Promise<any> {
        return LLM.service.generate(systemPrompt, userPrompt);
    }
}