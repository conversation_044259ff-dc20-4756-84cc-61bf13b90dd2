import { DelayedMessage } from '../models/types';
import WebSocket from 'ws';

// ===== STREAMING TYPES =====

export interface ChatResponse {
  reply: DelayedMessage[];
  theme?: string;
  skills?: string[];
  error?: string;
  details?: string;
  conversationId?: number;
}

export interface StreamingSession {
  ws: WebSocket;
  isStreaming: boolean;
  timeouts: NodeJS.Timeout[];
  interrupted: boolean;
  pendingMessages: DelayedMessage[];
  conversationId?: number;
  lastMessageId?: number;
  lastUserMessage?: string;
  pollInterval?: NodeJS.Timeout;
  autonomousTimer?: NodeJS.Timeout;
  extendedWorkflowTimer?: NodeJS.Timeout;
  extendedWorkflowActive: boolean;
  extendedWorkflowStartTime?: number;
  extendedWorkflowCharacterTimers: NodeJS.Timeout[];
  lastUserActivityTime?: number; // Timestamp of last user message
  engagementLevel: number; // 0-1 scale, starts at 1, decays over time
  sessionId: string; // Database session ID
  dbSession?: any; // Database session object
}

export interface ExtendedWorkflowStatus {
  active: boolean;
  timeRemaining: number;
  startTime?: number;
}

export interface EngagementInfo {
  engagementLevel: number;
  delayMultiplier: number;
  shouldTimeout: boolean;
}

// Message types for WebSocket communication
export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

export interface ChatMessage extends WebSocketMessage {
  type: 'chat';
  text: string;
}

export interface InterruptMessage extends WebSocketMessage {
  type: 'interrupt';
  text: string;
}

export interface ConnectedMessage extends WebSocketMessage {
  type: 'connected';
  sessionId: string;
}

export interface ChatStartMessage extends WebSocketMessage {
  type: 'chat_start';
  theme: string;
}

export interface MessageResponse extends WebSocketMessage {
  type: 'message';
  character: string;
  text: string;
  index: number;
  total: number;
}

export interface ChatCompleteMessage extends WebSocketMessage {
  type: 'chat_complete';
  skills: string[];
}

export interface TypingStartMessage extends WebSocketMessage {
  type: 'typing_start';
  character: string;
}

export interface TypingStopMessage extends WebSocketMessage {
  type: 'typing_stop';
  character: string;
}

export interface DelayedThoughtMessage extends WebSocketMessage {
  type: 'delayed_thought';
  character: string;
  text: string;
}

export interface ExtendedWorkflowStartMessage extends WebSocketMessage {
  type: 'extended_workflow_start';
  duration: number;
  message: string;
}

export interface ExtendedWorkflowEndMessage extends WebSocketMessage {
  type: 'extended_workflow_end';
  message: string;
}

export interface ExtendedWorkflowExtendedMessage extends WebSocketMessage {
  type: 'extended_workflow_extended';
  message: string;
  newDuration: number;
  timeRemaining: number;
}

export interface ExtendedWorkflowMessageResponse extends WebSocketMessage {
  type: 'extended_workflow_message';
  character: string;
  text: string;
  timeRemaining: number;
}

export interface ExtendedWorkflowStatusMessage extends WebSocketMessage {
  type: 'extended_workflow_status';
  active: boolean;
  timeRemaining: number;
  startTime?: number;
}

export interface AutonomousMessage extends WebSocketMessage {
  type: 'autonomous_message';
  character: string;
  text: string;
}

export interface ConversationTimeoutMessage extends WebSocketMessage {
  type: 'conversation_timeout';
  message: string;
}

export interface InterruptedMessage extends WebSocketMessage {
  type: 'interrupted';
  message: string;
  extendedWorkflowInterrupted: boolean;
}

export interface DelayedThoughtsAvailableMessage extends WebSocketMessage {
  type: 'delayed_thoughts_available';
  count: number;
}

export interface ErrorMessage extends WebSocketMessage {
  type: 'error';
  error: string;
  details?: string;
}
