import { DBOS } from '@dbos-inc/dbos-sdk';
import { ForaChat } from '../operations';
import { CharacterWorkflowService } from '../core/CharacterWorkflowService';
import { ConversationService } from '../core/ConversationService';
import { SessionManager } from './SessionManager';
import { MessageStreamer } from './MessageStreamer';
import { EngagementMonitor } from './EngagementMonitor';
import { ExtendedWorkflowStatus } from './types';
import WebSocket from 'ws';

// ===== WORKFLOW MANAGER COMPONENT =====

export class WorkflowManager {
  constructor(
    private sessionManager: SessionManager,
    private messageStreamer: MessageStreamer,
    private engagementMonitor: EngagementMonitor
  ) {}

  // Start the extended 10-minute workflow where characters continue to chime in
  async startExtendedWorkflow(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.conversationId) return;

    // Clear any existing autonomous timer since we're starting extended workflow
    if (session.autonomousTimer) {
      clearTimeout(session.autonomousTimer);
      session.autonomousTimer = undefined;
    }

    session.extendedWorkflowActive = true;
    session.extendedWorkflowStartTime = Date.now();

    // Notify the client that extended workflow has started
    this.messageStreamer.sendMessage(sessionId, {
      type: 'extended_workflow_start',
      duration: 600000, // 10 minutes in milliseconds
      message: 'Characters will continue the conversation for the next 10 minutes...'
    });

    // Set the main timer for 10 minutes
    session.extendedWorkflowTimer = setTimeout(() => {
      this.endExtendedWorkflow(sessionId);
    }, 600000); // 10 minutes

    // Schedule the first character interaction
    this.scheduleNextCharacterInteraction(sessionId);
  }

  // Schedule the next character interaction during extended workflow
  private scheduleNextCharacterInteraction(sessionId: string): void {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.extendedWorkflowActive || !session.conversationId) return;

    // Base delay between 15-45 seconds for varied pacing
    const baseDelay = 15000 + Math.random() * 30000;

    const timer = setTimeout(async () => {
      // Check if session is still active and extended workflow is running
      if (!session || !session.extendedWorkflowActive || !session.ws ||
          session.ws.readyState !== WebSocket.OPEN || session.isStreaming) {
        return;
      }

      try {
        // Check conversation engagement and apply decay
        const decayInfo = await this.engagementMonitor.checkConversationEngagement(session.conversationId!);

        if (decayInfo.shouldTimeout) {
          DBOS.logger.info(`Extended workflow conversation ${session.conversationId} timed out due to low engagement (${Math.round(decayInfo.engagementLevel * 100)}%). Ending extended workflow.`);
          this.endExtendedWorkflow(sessionId);
          return;
        }

        const adjustedDelay = this.engagementMonitor.applyDecayToDelay(baseDelay, decayInfo.delayMultiplier);

        if (adjustedDelay === Infinity) {
          DBOS.logger.info(`Extended workflow conversation ${session.conversationId} delay timeout. Ending extended workflow.`);
          this.endExtendedWorkflow(sessionId);
          return;
        }

        const lastActivity = await ConversationService.getLastUserActivity(session.conversationId!);
        const timeSinceActivity = lastActivity ? Date.now() - lastActivity.getTime() : 0;
        const decayStatus = this.engagementMonitor.getDecayStatus(
          timeSinceActivity,
          decayInfo.engagementLevel,
          decayInfo.delayMultiplier
        );

        DBOS.logger.info(`Extended workflow character interaction with decay: ${decayStatus}`);

        // Apply additional delay if needed
        if (adjustedDelay > baseDelay) {
          await new Promise(resolve => setTimeout(resolve, adjustedDelay - baseDelay));
        }

        // Get recent messages for context
        const recentMessages = await ConversationService.getDelayedThoughts(session.conversationId!);
        if (recentMessages.length === 0) {
          // Schedule next interaction even if no messages
          this.scheduleNextCharacterInteraction(sessionId);
          return;
        }

        // Choose a random character
        const characters = ['Fora', 'Jan', 'Lou'];
        const character = characters[Math.floor(Math.random() * characters.length)];

        // Build context from recent messages
        const context = recentMessages.slice(-5).map(msg => `${msg.character}: ${msg.text}`).join('\n');

        // Generate character thought for extended workflow (doesn't queue message)
        const handle = await DBOS.startWorkflow(CharacterWorkflowService).extendedWorkflowThoughtWorkflow(
          session.conversationId!,
          context,
          character
        );

        const result = await handle.getResult();
        if (result && result.text) {
          // Show typing indicator first
          this.messageStreamer.sendMessage(sessionId, {
            type: 'typing_start',
            character: result.character
          });

          // Wait 3 seconds, then send the message
          setTimeout(async () => {
            if (session && session.extendedWorkflowActive && session.ws && session.ws.readyState === WebSocket.OPEN) {
              // Stop typing indicator
              this.messageStreamer.sendMessage(sessionId, {
                type: 'typing_stop',
                character: result.character
              });

              // Send the extended workflow message
              this.messageStreamer.sendMessage(sessionId, {
                type: 'extended_workflow_message',
                character: result.character,
                text: result.text,
                timeRemaining: this.getExtendedWorkflowTimeRemaining(sessionId)
              });

              // Add the message to conversation history since it won't be queued
              try {
                const messageExists = await ConversationService.checkMessageExists(
                  result.character,
                  result.text,
                  session.conversationId!
                );

                if (!messageExists) {
                  await ConversationService.addMessage(result.character, result.text, session.conversationId!);
                  DBOS.logger.info(`Added extended workflow message to conversation history: ${result.character}: ${result.text.substring(0, 50)}...`);
                } else {
                  DBOS.logger.info(`Skipped adding extended workflow message (already in conversation history): ${result.character}: ${result.text.substring(0, 50)}...`);
                }
              } catch (error) {
                DBOS.logger.error(`Error adding extended workflow message to conversation history: ${(error as Error).message}`);
              }
            }
          }, 3000);

          // Schedule the next interaction
          this.scheduleNextCharacterInteraction(sessionId);
        } else {
          // If no result, still schedule next interaction
          this.scheduleNextCharacterInteraction(sessionId);
        }
      } catch (error) {
        console.error('Error in extended workflow character interaction:', error);
        // Schedule next interaction even on error
        this.scheduleNextCharacterInteraction(sessionId);
      }
    }, baseDelay);

    session.extendedWorkflowCharacterTimers.push(timer);
  }

  // End the extended workflow
  private endExtendedWorkflow(sessionId: string): void {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    session.extendedWorkflowActive = false;

    // Clear all extended workflow timers
    if (session.extendedWorkflowTimer) {
      clearTimeout(session.extendedWorkflowTimer);
      session.extendedWorkflowTimer = undefined;
    }

    session.extendedWorkflowCharacterTimers.forEach(timer => clearTimeout(timer));
    session.extendedWorkflowCharacterTimers = [];

    // Notify the client that extended workflow has ended
    this.messageStreamer.sendMessage(sessionId, {
      type: 'extended_workflow_end',
      message: 'Extended conversation period has ended.'
    });

    // Resume normal autonomous interaction scheduling
    this.scheduleAutonomousInteraction(sessionId);
  }

  private scheduleAutonomousInteraction(sessionId: string): void {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.conversationId) return;

    // Clear any existing autonomous interaction timer
    if (session.autonomousTimer) {
      clearTimeout(session.autonomousTimer);
    }

    // Set a timer for autonomous interaction (e.g., 30 seconds of inactivity)
    session.autonomousTimer = setTimeout(async () => {
      // Only proceed if the session is still active and not streaming
      if (!session || !session.ws || session.ws.readyState !== WebSocket.OPEN || session.isStreaming) {
        return;
      }

      try {
        // Check conversation engagement and apply decay
        const decayInfo = await this.engagementMonitor.checkConversationEngagement(session.conversationId!);

        if (decayInfo.shouldTimeout) {
          DBOS.logger.info(`Autonomous conversation ${session.conversationId} timed out due to low engagement (${Math.round(decayInfo.engagementLevel * 100)}%). Stopping autonomous interactions.`);
          // Stop all character interactions and notify client
          this.stopAllCharacterInteractions(sessionId);
          this.messageStreamer.sendMessage(sessionId, {
            type: 'conversation_timeout',
            message: 'Conversation has ended due to inactivity.'
          });
          return;
        }

        const lastActivity = await ConversationService.getLastUserActivity(session.conversationId!);
        const timeSinceActivity = lastActivity ? Date.now() - lastActivity.getTime() : 0;
        const decayStatus = this.engagementMonitor.getDecayStatus(
          timeSinceActivity,
          decayInfo.engagementLevel,
          decayInfo.delayMultiplier
        );

        DBOS.logger.info(`Autonomous interaction with decay: ${decayStatus}`);
        // Get recent messages to use as context
        const recentMessages = await ConversationService.getDelayedThoughts(session.conversationId!);
        if (recentMessages.length === 0) return;

        // Choose a random character to continue the conversation
        const characters = ['Fora', 'Jan', 'Lou'];
        const character = characters[Math.floor(Math.random() * characters.length)];

        // Build context from recent messages
        const context = recentMessages.slice(-5).map(msg => `${msg.character}: ${msg.text}`).join('\n');

        // Generate an autonomous thought
        const handle = await DBOS.startWorkflow(CharacterWorkflowService).characterThoughtWorkflow(
          session.conversationId!,
          context,
          character
        );

        const result = await handle.getResult();
        if (result && result.text) {
          // Send the autonomous message
          this.messageStreamer.sendMessage(sessionId, {
            type: 'autonomous_message',
            character: result.character,
            text: result.text
          });

          // Schedule another autonomous interaction
          this.scheduleAutonomousInteraction(sessionId);
        }
      } catch (error) {
        console.error('Error in autonomous interaction:', error);
      }
    }, 30000); // 30 seconds of inactivity
  }

  // Stop all character interactions for a conversation that has timed out
  private stopAllCharacterInteractions(sessionId: string): void {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    // Clear autonomous timer
    if (session.autonomousTimer) {
      clearTimeout(session.autonomousTimer);
      session.autonomousTimer = undefined;
    }

    // Clear extended workflow if active
    if (session.extendedWorkflowActive) {
      this.endExtendedWorkflow(sessionId);
    }

    DBOS.logger.info(`Stopped all character interactions for session ${sessionId} due to conversation timeout`);
  }

  // Get remaining time in extended workflow
  private getExtendedWorkflowTimeRemaining(sessionId: string): number {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.extendedWorkflowActive || !session.extendedWorkflowStartTime) {
      return 0;
    }

    const elapsed = Date.now() - session.extendedWorkflowStartTime;
    const remaining = Math.max(0, 600000 - elapsed); // 10 minutes - elapsed
    return remaining;
  }

  // Extend the workflow timeout when user is active
  extendWorkflowTimeout(sessionId: string): void {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.extendedWorkflowActive) return;

    // Clear the existing main timer
    if (session.extendedWorkflowTimer) {
      clearTimeout(session.extendedWorkflowTimer);
    }

    // Reset the start time to now (effectively extending by 10 minutes from now)
    session.extendedWorkflowStartTime = Date.now();

    // Set a new 10-minute timer
    session.extendedWorkflowTimer = setTimeout(() => {
      this.endExtendedWorkflow(sessionId);
    }, 600000); // 10 minutes

    // Notify the client that the workflow has been extended
    this.messageStreamer.sendMessage(sessionId, {
      type: 'extended_workflow_extended',
      message: 'Extended conversation period renewed for another 10 minutes due to your activity!',
      newDuration: 600000,
      timeRemaining: 600000
    });
  }

  // Public method to manually start extended workflow (for testing or manual triggers)
  async triggerExtendedWorkflow(sessionId: string): Promise<boolean> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.conversationId || session.extendedWorkflowActive) {
      return false;
    }

    await this.startExtendedWorkflow(sessionId);
    return true;
  }

  // Public method to get extended workflow status
  getExtendedWorkflowStatus(sessionId: string): ExtendedWorkflowStatus {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) {
      return { active: false, timeRemaining: 0 };
    }

    return {
      active: session.extendedWorkflowActive,
      timeRemaining: this.getExtendedWorkflowTimeRemaining(sessionId),
      startTime: session.extendedWorkflowStartTime
    };
  }
}
