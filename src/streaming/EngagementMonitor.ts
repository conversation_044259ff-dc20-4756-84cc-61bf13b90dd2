import { DBOS } from '@dbos-inc/dbos-sdk';
import { ForaChat } from '../operations';
import { ConversationService } from '../core/ConversationService';
import { ConversationDecayService } from '../core/ConversationDecayService';
import { SessionManager } from './SessionManager';
import { EngagementInfo } from './types';
import WebSocket from 'ws';

// ===== ENGAGEMENT MONITOR COMPONENT =====

export class EngagementMonitor {
  constructor(private sessionManager: SessionManager) {
    // Start periodic engagement level updates for all sessions
    this.startEngagementMonitoring();
  }

  // Periodically update engagement levels for all active sessions
  private startEngagementMonitoring(): void {
    setInterval(async () => {
      const sessions = this.sessionManager.getAllSessions();
      for (const [sessionId, session] of sessions.entries()) {
        if (session.conversationId && session.ws.readyState === WebSocket.OPEN) {
          try {
            // Update engagement level based on current activity
            const timeSinceActivity = session.lastUserActivityTime ?
              Date.now() - session.lastUserActivityTime : 0;

            const newEngagementLevel = ConversationDecayService.calculateEngagementLevel(timeSinceActivity);

            // Update session engagement level
            session.engagementLevel = newEngagementLevel;

            // Update database if engagement has significantly changed
            if (Math.abs(session.engagementLevel - newEngagementLevel) > 0.1) {
              await ConversationService.updateEngagementLevel(session.conversationId, newEngagementLevel);
            }
          } catch (error) {
            DBOS.logger.error(`Error updating engagement for session ${sessionId}: ${(error as Error).message}`);
          }
        }
      }
    }, 30000); // Update every 30 seconds
  }

  updateUserActivity(sessionId: string): void {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    // Update user activity tracking
    session.lastUserActivityTime = Date.now();
    session.engagementLevel = 1.0; // Reset to full engagement on user activity
  }

  async updateSessionActivity(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    try {
      await ForaChat.updateSessionActivity(session.sessionId);
    } catch (error) {
      DBOS.logger.error(`Failed to update session activity: ${(error as Error).message}`);
    }

    // Update database activity tracking if conversation exists
    if (session.conversationId) {
      await ConversationService.updateUserActivity(session.conversationId);
    }
  }

  async checkConversationEngagement(conversationId: number): Promise<EngagementInfo> {
    return await ConversationDecayService.updateConversationEngagement(conversationId);
  }

  getDecayStatus(timeSinceActivity: number, engagementLevel: number, delayMultiplier: number): string {
    return ConversationDecayService.getDecayStatus(timeSinceActivity, engagementLevel, delayMultiplier);
  }

  applyDecayToDelay(baseDelay: number, delayMultiplier: number): number {
    return ConversationDecayService.applyDecayToDelay(baseDelay, delayMultiplier);
  }
}
