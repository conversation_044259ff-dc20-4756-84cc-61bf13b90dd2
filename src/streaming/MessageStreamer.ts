import { DBOS } from '@dbos-inc/dbos-sdk';
import { DelayedMessage, QueuedMessage } from '../models/types';
import { ConversationService } from '../core/ConversationService';
import { MessageQueueService } from '../core/MessageQueueService';
import { SessionManager } from './SessionManager';
import { WebSocketMessage } from './types';
import WebSocket from 'ws';

// ===== MESSAGE STREAMER COMPONENT =====

export class MessageStreamer {
  constructor(private sessionManager: SessionManager) {}

  async streamDelayedMessages(
    sessionId: string,
    messages: DelayedMessage[],
    skills?: string[]
  ): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    session.isStreaming = true;
    session.interrupted = false;
    session.pendingMessages = [...messages];

    // Handle empty messages case (e.g., general greetings)
    if (messages.length === 0) {
      // Send chat_complete immediately and start extended workflow
      setTimeout(async () => {
        if (!session.interrupted) {
          this.sendMessage(sessionId, {
            type: 'chat_complete',
            skills: skills || []
          });
          session.isStreaming = false;
          session.pendingMessages = [];
        }
      }, 100);
      return;
    }

    let cumulativeDelay = 0;
    const TYPING_INDICATOR_LEAD_TIME = 3000; // Show typing indicator 3 seconds before message

    messages.forEach((message, index) => {
      const delay = message.delay || 0;
      cumulativeDelay += delay; // Delay is already in milliseconds

      // Schedule typing indicator to show before the message
      // Ensure typing indicator never shows immediately (minimum 1 second delay)
      const typingStartTime = Math.max(1000, cumulativeDelay - TYPING_INDICATOR_LEAD_TIME);
      const typingTimeout = setTimeout(() => {
        if (session.interrupted) {
          return; // Don't show typing if interrupted
        }

        this.sendMessage(sessionId, {
          type: 'typing_start',
          character: message.character
        });
      }, typingStartTime);

      session.timeouts.push(typingTimeout);

      // Schedule the actual message
      const messageTimeout = setTimeout(() => {
        if (session.interrupted) {
          return; // Don't send if interrupted
        }

        // Stop typing indicator just before sending message
        this.sendMessage(sessionId, {
          type: 'typing_stop',
          character: message.character
        });

        this.sendMessage(sessionId, {
          type: 'message',
          character: message.character,
          text: message.text,
          index,
          total: messages.length
        });

        // If this is the last message, finish the stream
        if (index === messages.length - 1) {
          setTimeout(async () => {
            if (!session.interrupted) {
              this.sendMessage(sessionId, {
                type: 'chat_complete',
                skills: skills || []
              });
              session.isStreaming = false;
              session.pendingMessages = [];
            }
          }, 100);
        }
      }, cumulativeDelay);

      session.timeouts.push(messageTimeout);
    });
  }

  async streamQueuedMessages(sessionId: string, readyMessages: QueuedMessage[]): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    // Sort messages by their priority and scheduled time
    const sortedMessages = [...readyMessages].sort((a, b) => {
      if (a.priority !== b.priority) return a.priority - b.priority;
      return new Date(a.scheduled_at!).getTime() - new Date(b.scheduled_at!).getTime();
    });

    let cumulativeDelay = 0;
    const TYPING_LEAD_TIME = 3000; // 3 seconds before message
    const INITIAL_DELAY = 1000; // Initial delay before first message

    for (let i = 0; i < sortedMessages.length; i++) {
      const message = sortedMessages[i];

      // For the first message, use initial delay
      // For subsequent messages, use a base delay plus some spacing
      if (i === 0) {
        cumulativeDelay += INITIAL_DELAY;
      } else {
        // Add spacing between messages (minimum 2 seconds)
        cumulativeDelay += Math.max(2000, message.delay_ms / 2);
      }

      // Schedule typing indicator - ensure it's always at least a few seconds from now
      const typingDelay = Math.max(500, cumulativeDelay - TYPING_LEAD_TIME);
      setTimeout(() => {
        if (session && session.ws && session.ws.readyState === WebSocket.OPEN && !session.isStreaming) {
          this.sendMessage(sessionId, {
            type: 'typing_start',
            character: message.character
          });
        }
      }, typingDelay);

      // Schedule message
      setTimeout(async () => {
        if (session && session.ws && session.ws.readyState === WebSocket.OPEN && !session.isStreaming) {
          // Stop typing indicator
          this.sendMessage(sessionId, {
            type: 'typing_stop',
            character: message.character
          });

          // Send the message
          this.sendMessage(sessionId, {
            type: 'delayed_thought',
            character: message.character,
            text: message.text
          });

          // Mark message as sent and add to conversation history (if not already there)
          await MessageQueueService.updateMessageStatus(message.id, 'SENT');

          // Check if this message already exists in conversation history to prevent duplicates
          const messageExists = await ConversationService.checkMessageExists(
            message.character,
            message.text,
            message.conversation_id
          );

          if (!messageExists) {
            await ConversationService.addMessage(message.character, message.text, message.conversation_id);
            DBOS.logger.info(`Added queued message to conversation history: ${message.character}: ${message.text.substring(0, 50)}...`);
          } else {
            DBOS.logger.info(`Skipped adding queued message (already in conversation history): ${message.character}: ${message.text.substring(0, 50)}...`);
          }
        }
      }, cumulativeDelay);
    }
  }

  sendMessage(sessionId: string, message: WebSocketMessage): void {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || session.ws.readyState !== WebSocket.OPEN) return;

    session.ws.send(JSON.stringify(message));
  }

  sendError(sessionId: string, error: string, details?: string): void {
    this.sendMessage(sessionId, {
      type: 'error',
      error,
      details
    });
  }
}
