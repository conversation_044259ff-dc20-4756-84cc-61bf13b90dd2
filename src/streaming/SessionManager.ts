import { DBOS } from '@dbos-inc/dbos-sdk';
import { ForaChat } from '../operations';
import { DelayedMessage } from '../models/types';
import { MessageQueueService } from '../core/MessageQueueService';
import { StreamingSession } from './types';
import WebSocket from 'ws';

// ===== SESSION MANAGER COMPONENT =====

export class SessionManager {
  private sessions: Map<string, StreamingSession> = new Map();

  createSession(sessionId: string, ws: WebSocket, dbSession?: any): StreamingSession {
    const session: StreamingSession = {
      ws,
      isStreaming: false,
      timeouts: [],
      interrupted: false,
      pendingMessages: [],
      extendedWorkflowActive: false,
      extendedWorkflowCharacterTimers: [],
      lastUserActivityTime: Date.now(),
      engagementLevel: 1.0, // Start with full engagement
      sessionId,
      dbSession,
      conversationId: dbSession?.conversation_id
    };

    this.sessions.set(sessionId, session);

    // If reconnecting to an existing session, restore workflow state
    if (dbSession?.conversation_id) {
      this.restoreSessionWorkflowState(sessionId);
    }

    return session;
  }

  getSession(sessionId: string): StreamingSession | undefined {
    return this.sessions.get(sessionId);
  }

  getAllSessions(): Map<string, StreamingSession> {
    return this.sessions;
  }

  deleteSession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }

  getSessionCount(): number {
    return this.sessions.size;
  }

  private async restoreSessionWorkflowState(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session || !session.conversationId) return;

    try {
      // Get conversation engagement level and activity
      const handle = await DBOS.startWorkflow(ForaChat).updateConversationEngagement(session.conversationId);
      const engagementData = await handle.getResult();

      session.engagementLevel = engagementData.engagementLevel;

      // Check if there are pending messages in the queue for this conversation
      const pendingMessages = await MessageQueueService.getPendingMessages(session.conversationId);

      if (pendingMessages.length > 0) {
        DBOS.logger.info(`Restoring ${pendingMessages.length} pending messages for session ${sessionId}`);

        // Convert queued messages to delayed messages format
        const delayedMessages = pendingMessages.map(msg => ({
          character: msg.character,
          text: msg.text,
          delay: msg.delay_ms || 3000
        }));

        // Store these for the streaming service to handle
        session.pendingMessages = delayedMessages;
      }

      DBOS.logger.info(`Restored workflow state for session ${sessionId} with engagement level ${session.engagementLevel}`);
    } catch (error) {
      DBOS.logger.error(`Error restoring session workflow state: ${(error as Error).message}`);
    }
  }

  clearTimeouts(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.timeouts.forEach(timeout => clearTimeout(timeout));
    session.timeouts = [];

    // Clear extended workflow timers
    if (session.extendedWorkflowTimer) {
      clearTimeout(session.extendedWorkflowTimer);
      session.extendedWorkflowTimer = undefined;
    }

    session.extendedWorkflowCharacterTimers.forEach(timeout => clearTimeout(timeout));
    session.extendedWorkflowCharacterTimers = [];
    session.extendedWorkflowActive = false;
  }

  cleanupSession(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      this.clearTimeouts(sessionId);

      // Clear polling interval
      if (session.pollInterval) {
        clearInterval(session.pollInterval);
      }

      // Clear autonomous timer
      if (session.autonomousTimer) {
        clearTimeout(session.autonomousTimer);
      }

      this.sessions.delete(sessionId);
    }
  }
}
