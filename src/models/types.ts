// Core data models and types

export interface Conversation {
  id: number;
  created_at: Date;
  updated_at: Date;
  theme?: string;
  skills?: string[] | string;
  last_user_activity?: Date;
  engagement_level?: number; // 0-1 scale, starts at 1, decays over time
}

export interface Message {
  id: number;
  character: string;
  text: string;
  conversation_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface ChatMessage {
  character: string;
  text: string;
  delay: number;
}

export interface DelayedMessage {
  id?: number;
  character: string;
  text: string;
  delay?: number;
  created_at?: Date;
}

export interface ChatResponse {
  reply: ChatMessage[];
  skills: string[];
  theme: string;
}

export interface UserRequest {
  text: string;
  userId?: string;
  sessionId?: string;
}

export interface Session {
  id: string;
  user_identifier: string;
  channel: 'web' | 'repl' | 'sms' | 'slack' | 'teams';
  conversation_id?: number;
  created_at: Date;
  updated_at: Date;
  last_activity: Date;
  expires_at?: Date;
  metadata?: Record<string, any>;
}

export interface SessionCreateRequest {
  userIdentifier: string;
  channel: 'web' | 'repl' | 'sms' | 'slack' | 'teams';
  conversationId?: number;
  metadata?: Record<string, any>;
  expiresAt?: Date;
}

export interface ChatResult {
  response: ChatResponse;
  conversationId: number;
  messageId: number;
}

// Interface for different messaging platforms
export interface MessageInterface {
  sendMessage(message: string): Promise<void>;
  receiveMessage(): Promise<string>;
  formatResponse(response: ChatResponse): string;
}

// Configuration types
export interface DatabaseConfig {
  url: string;
  client: string;
}

export interface LLMConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
}

export interface AppConfig {
  database: DatabaseConfig;
  llm: LLMConfig;
  port: number;
  environment: 'development' | 'production' | 'test';
}

// Message Queue types
export type MessageQueueStatus = 'PENDING' | 'PROCESSING' | 'SENT' | 'CANCELLED';

export interface QueuedMessage {
  id: number;
  conversation_id: number;
  character: string;
  text: string;
  delay_ms: number;
  status: MessageQueueStatus;
  priority: number;
  similarity_hash?: string;
  similarity_score?: number;
  scheduled_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface QueueMessageInput {
  conversation_id: number;
  character: string;
  text: string;
  delay_ms?: number;
  priority?: number;
  scheduled_at?: Date;
}

export interface SimilarityCheck {
  isDuplicate: boolean;
  isSimilar: boolean;
  similarityScore: number;
  existingMessage?: QueuedMessage;
}
