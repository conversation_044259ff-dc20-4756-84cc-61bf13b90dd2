import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'
import { MessageProps } from '../components/Message'

interface ChatMessage {
  type: string
  sessionId?: string
  theme?: string
  character?: string
  text?: string
  message?: string
  error?: string
  details?: string
  skills?: string[]
  count?: number
}

interface ChatContextType {
  messages: MessageProps[]
  skills: string[]
  connectionStatus: string
  isConnected: boolean
  isStreaming: boolean
  isTyping: boolean
  typingCharacter: string | null
  isAtBottom: boolean
  sessionId: string | null
  isRestoringSession: boolean
  setIsAtBottom: (isAtBottom: boolean) => void
  sendMessage: (text: string) => void
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export const useChat = () => {
  const context = useContext(ChatContext)
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context
}

interface ChatProviderProps {
  children: ReactNode
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  const [ws, setWs] = useState<WebSocket | null>(null)
  const [messages, setMessages] = useState<MessageProps[]>([])
  const [skills, setSkills] = useState<string[]>([])
  const [connectionStatus, setConnectionStatus] = useState('Connecting...')
  const [isConnected, setIsConnected] = useState(false)
  const [isStreaming, setIsStreaming] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [typingCharacter, setTypingCharacter] = useState<string | null>(null)
  const [isAtBottom, setIsAtBottom] = useState(true)
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [isRestoringSession, setIsRestoringSession] = useState(false)

  // Session restoration function
  const restoreSession = useCallback(async () => {
    try {
      setIsRestoringSession(true)
      const response = await fetch('/session/conversations')
      if (response.ok) {
        const data = await response.json()
        if (data.messages && data.messages.length > 0) {
          console.log('📜 Restoring conversation history:', data.messages.length, 'messages')

          // Convert database messages to UI format
          const restoredMessages: MessageProps[] = data.messages.map((msg: any) => ({
            type: msg.character === 'user' ? 'user' : 'assistant',
            character: msg.character,
            text: msg.text,
            timestamp: new Date(msg.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
          }))

          setMessages(restoredMessages)
          console.log('✅ Session restored with', restoredMessages.length, 'messages')
        }
      }
    } catch (error) {
      console.error('❌ Error restoring session:', error)
    } finally {
      setIsRestoringSession(false)
    }
  }, [])

  const connect = useCallback(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${protocol}//${window.location.host}`

    console.log('🔗 CONNECTING TO WEBSOCKET:', wsUrl)

    const websocket = new WebSocket(wsUrl)

    websocket.onopen = () => {
      setConnectionStatus('Connected')
      setIsConnected(true)
      console.log('✅ WebSocket connected successfully')
    }

    websocket.onmessage = (event) => {
      console.log('📨 RAW WEBSOCKET MESSAGE:', event.data)
      try {
        const message: ChatMessage = JSON.parse(event.data)
        console.log('📨 PARSED MESSAGE:', message)
        handleMessage(message)
      } catch (error) {
        console.error('❌ ERROR PARSING MESSAGE:', error, event.data)
      }
    }

    websocket.onclose = () => {
      console.log('❌ WebSocket connection closed')
      setConnectionStatus('Disconnected')
      setIsConnected(false)
      setWs(null)
      setTimeout(connect, 3000)
    }

    websocket.onerror = (error) => {
      console.error('❌ WebSocket error:', error)
      setConnectionStatus('Connection Error')
      setIsConnected(false)
    }

    setWs(websocket)
  }, [])

  const handleMessage = useCallback((message: ChatMessage) => {
    const now = new Date()
    const timestamp = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })

    console.log('🔥 FRONTEND RECEIVED MESSAGE:', message.type, message)

    switch (message.type) {
      case 'connected':
        console.log('🔗 Connected with session ID:', message.sessionId)
        if (message.sessionId) {
          setSessionId(message.sessionId)
          // Restore session if we have a session ID
          restoreSession()
        }
        break
        
      case 'chat_start':
        setIsStreaming(true)
        if (message.theme) {
          addMessage('system', 'System', `--- ${message.theme} ---`)
        }
        break
        
      case 'message':
        console.log('💬 REGULAR MESSAGE:', message.character, message.text?.substring(0, 50) + '...')
        if (message.character && message.text) {
          addMessage('assistant', message.character, message.text, timestamp)
        }
        break

      case 'autonomous_message':
        console.log('🤖 AUTONOMOUS MESSAGE:', message.character, message.text?.substring(0, 50) + '...')
        if (message.character && message.text) {
          addMessage('assistant', message.character, message.text, timestamp)
        }
        break

      case 'delayed_thought':
        console.log('🔥 DELAYED THOUGHT RECEIVED:', message.character, message.text?.substring(0, 50) + '...')
        console.log('🔥 FULL DELAYED THOUGHT MESSAGE:', message)
        if (message.character && message.text) {
          console.log('🔥 CALLING addMessage FOR DELAYED THOUGHT')
          addMessage('assistant', message.character, message.text, timestamp)
        } else {
          console.log('❌ DELAYED THOUGHT MISSING CHARACTER OR TEXT:', { character: message.character, text: message.text })
        }
        break

      case 'delayed_thoughts_available':
        console.log(`🔔 ${message.count} delayed thoughts available`)
        break

      case 'typing_start':
        if (message.character) {
          setIsTyping(true)
          setTypingCharacter(message.character)
        }
        break

      case 'typing_stop':
        setIsTyping(false)
        setTypingCharacter(null)
        break

      case 'chat_complete':
        setIsStreaming(false)
        setIsTyping(false)
        setTypingCharacter(null)
        if (message.skills && message.skills.length > 0) {
          setSkills(message.skills)
        }
        break
        
      case 'interrupted':
        if (message.message) {
          addMessage('system', 'System', message.message)
        }
        break

      case 'extended_workflow_start':
        // Hide the "Characters will continue the conversation for the next 10 minutes..." notification
        console.log('🔕 Extended workflow started (notification hidden)')
        break

      case 'extended_workflow_message':
        if (message.character && message.text) {
          addMessage('assistant', message.character, message.text, timestamp)
        }
        break

      case 'extended_workflow_end':
        // Hide the "Extended conversation period has ended." notification
        console.log('🔕 Extended workflow ended (notification hidden)')
        break

      case 'extended_workflow_extended':
        // Hide the "Extended conversation period renewed for another 10 minutes..." notification
        console.log('🔕 Extended workflow extended (notification hidden)')
        break

      case 'error':
        setIsStreaming(false)
        setIsTyping(false)
        setTypingCharacter(null)
        const errorText = `${message.error}${message.details ? ': ' + message.details : ''}`
        addMessage('error', 'Error', errorText)
        break
    }
  }, [])

  const addMessage = useCallback((type: MessageProps['type'], character: string, text: string, timestamp?: string) => {
    const newMessage: MessageProps = {
      type,
      character,
      text,
      timestamp
    }
    console.log('➕ ADDING MESSAGE TO UI:', newMessage)
    setMessages(prev => {
      const updated = [...prev, newMessage]
      console.log('📝 UPDATED MESSAGES ARRAY:', updated.length, 'messages')
      return updated
    })
  }, [])

  const sendMessage = useCallback((text: string) => {
    if (!ws || ws.readyState !== WebSocket.OPEN) return

    const now = new Date()
    const timestamp = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })

    // Add user message to UI
    addMessage('user', 'You', text, timestamp)

    // Send to server
    if (isStreaming) {
      ws.send(JSON.stringify({ type: 'interrupt', text }))
    } else {
      ws.send(JSON.stringify({ type: 'chat', text }))
    }

    // Don't immediately show typing - let the server control when typing indicators appear
  }, [ws, isStreaming, addMessage])



  useEffect(() => {
    connect()
    
    return () => {
      if (ws) {
        ws.close()
      }
    }
  }, [connect])

  const value: ChatContextType = {
    messages,
    skills,
    connectionStatus,
    isConnected,
    isStreaming,
    isTyping,
    typingCharacter,
    isAtBottom,
    sessionId,
    isRestoringSession,
    setIsAtBottom,
    sendMessage
  }

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  )
}
