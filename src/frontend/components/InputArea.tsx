import React, { useState } from 'react'
import { useChat } from '../hooks/useChat'

const InputArea: React.FC = () => {
  const [inputValue, setInputValue] = useState('')
  const { sendMessage, isConnected, isStreaming } = useChat()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (inputValue.trim() && isConnected) {
      sendMessage(inputValue.trim())
      setInputValue('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const isDisabled = !isConnected

  return (
    <div className="input-area">
      <input
        type="text"
        className="input-field"
        placeholder="Message"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyPress={handleKeyPress}
        disabled={isDisabled}
      />
      <button
        className="send-btn"
        onClick={handleSubmit}
        disabled={isDisabled || !inputValue.trim()}
      >
        ➤
      </button>
    </div>
  )
}

export default InputArea
