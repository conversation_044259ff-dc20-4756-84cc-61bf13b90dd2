import React from 'react'
import { useChat } from '../hooks/useChat'

const TypingIndicator: React.FC = () => {
  const { isTyping, typing<PERSON>haracter, isAtBottom } = useChat()

  if (!isTyping || !typingCharacter || !isAtBottom) {
    return null
  }

  const getAvatarLetter = () => typingCharacter.charAt(0).toUpperCase()

  return (
    <div className={`typing-indicator ${isTyping ? 'show' : ''}`}>
      <div className={`avatar ${typingCharacter.toLowerCase()}`}>
        {getAvatarLetter()}
      </div>
      <div className="typing-dots">
        <div className="dot"></div>
        <div className="dot"></div>
        <div className="dot"></div>
      </div>
    </div>
  )
}

export default TypingIndicator
