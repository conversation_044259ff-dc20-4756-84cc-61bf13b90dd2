import React from 'react'

export interface MessageProps {
  type: 'user' | 'assistant' | 'system' | 'error'
  character: string
  text: string
  timestamp?: string
}

const Message: React.FC<MessageProps> = ({ type, character, text, timestamp }) => {
  const getMessageClass = () => {
    if (type === 'user') return 'message user'
    if (type === 'assistant') return `message ${character.toLowerCase()}`
    return `message ${type}`
  }

  const getAvatarLetter = () => character.charAt(0).toUpperCase()

  if (type === 'user') {
    return (
      <div className={getMessageClass()}>
        <div className="message-content">
          <div className="bubble">{text}</div>
          {timestamp && <div className="timestamp">{timestamp}</div>}
        </div>
      </div>
    )
  }

  if (type === 'assistant') {
    return (
      <div className={getMessageClass()}>
        <div className={`avatar ${character.toLowerCase()}`}>
          {getAvatarLetter()}
        </div>
        <div className="message-content">
          <div className="sender-name">{character}</div>
          <div className="bubble">{text}</div>
          {timestamp && <div className="timestamp">{timestamp}</div>}
        </div>
      </div>
    )
  }

  // System and error messages
  return (
    <div className={getMessageClass()}>
      <div className="bubble">{text}</div>
    </div>
  )
}

export default Message
