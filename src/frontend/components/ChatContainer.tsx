import React, { useEffect, useRef, useState, useCallback } from 'react'
import { useChat } from '../hooks/useChat'
import Message from './Message'
import Skills from './Skills'

const ChatContainer: React.FC = () => {
  const { messages, skills, isAtBottom, setIsAtBottom } = useChat()
  const containerRef = useRef<HTMLDivElement>(null)
  const [showScrollNotification, setShowScrollNotification] = useState(false)
  const previousMessageCount = useRef(messages.length)

  const checkIfAtBottom = useCallback(() => {
    if (!containerRef.current) return true

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current
    const threshold = 50 // pixels from bottom to consider "at bottom"
    return scrollHeight - scrollTop - clientHeight <= threshold
  }, [])

  const scrollToBottom = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight
      setShowScrollNotification(false)
    }
  }, [])

  const handleScroll = useCallback(() => {
    const atBottom = checkIfAtBottom()
    setIsAtBottom(atBottom)

    if (atBottom) {
      setShowScrollNotification(false)
    }
  }, [checkIfAtBottom])

  useEffect(() => {
    const container = containerRef.current
    if (container) {
      container.addEventListener('scroll', handleScroll)
      return () => container.removeEventListener('scroll', handleScroll)
    }
  }, [handleScroll])

  useEffect(() => {
    // Check if new messages were added
    if (messages.length > previousMessageCount.current) {
      if (isAtBottom) {
        // Auto-scroll if user is at bottom
        scrollToBottom()
      } else {
        // Show notification if user is not at bottom
        setShowScrollNotification(true)
      }
    }
    previousMessageCount.current = messages.length
  }, [messages, isAtBottom, scrollToBottom])

  return (
    <div className="chat-container-wrapper">
      <div
        className="chat-container"
        ref={containerRef}
      >
        <div className="message system">
          <div className="bubble">
            Welcome! Type a message to start chatting. Messages will appear with realistic delays.
          </div>
        </div>

        {messages.map((message, index) => (
          <Message key={index} {...message} />
        ))}

        {skills.length > 0 && <Skills skills={skills} />}
      </div>

      {showScrollNotification && (
        <div className="scroll-notification" onClick={scrollToBottom}>
          <div className="scroll-notification-icon">↓</div>
        </div>
      )}
    </div>
  )
}

export default ChatContainer
