# Conversation Decay Implementation

This document describes the conversation decay system that gradually slows down character responses when users aren't engaging, eventually timing out conversations completely.

## Overview

The decay system implements exponential backoff for character responses based on user inactivity:
- **Active Phase (0-2 minutes)**: Normal response timing
- **Light Decay (2-5 minutes)**: 1.2x - 2x slower responses  
- **Moderate Decay (5-10 minutes)**: 2x - 4x slower responses
- **Heavy Decay (10-15 minutes)**: 4x - 8x slower responses
- **Timeout (15+ minutes)**: All character interactions stop

## Key Components

### 1. ConversationDecayService (`src/core/ConversationDecayService.ts`)
- **calculateDelayMultiplier()**: Computes exponential delay multipliers
- **calculateEngagementLevel()**: Tracks engagement from 1.0 (active) to 0.0 (inactive)
- **updateConversationEngagement()**: Updates database and returns decay info
- **applyDecayToDelay()**: Applies multiplier to base delays
- **getDecayStatus()**: Human-readable decay status for logging

### 2. Database Schema Updates
- **last_user_activity**: Timestamp of last user message
- **engagement_level**: 0-1 scale tracking conversation engagement
- Migration: `migrations/add_conversation_engagement_fields.sql`

### 3. Streaming Service Updates (`src/streaming.ts`)
- **User Activity Tracking**: Updates timestamps on every user message
- **Engagement Monitoring**: Periodic background updates every 30 seconds
- **Extended Workflow Decay**: Applies decay to extended workflow interactions
- **Autonomous Interaction Decay**: Applies decay to autonomous character thoughts
- **Complete Timeout**: Stops all interactions when engagement drops too low

### 4. Operations Updates (`src/operations.ts`)
- **Delayed Thought Scheduling**: Applies decay to character thought workflows
- **Decay-Aware Delays**: Checks engagement before scheduling character responses
- **Timeout Handling**: Skips character thoughts when conversation has timed out

## Configuration

Default decay parameters (configurable in `ConversationDecayService`):
```javascript
{
  initialDecayThreshold: 2 * 60 * 1000,      // 2 minutes - when decay starts
  maxDecayThreshold: 15 * 60 * 1000,         // 15 minutes - when timeout occurs
  minDelayMultiplier: 1.0,                   // No change when active
  maxDelayMultiplier: 8.0,                   // 8x slower at maximum decay
  decayExponent: 1.5,                        // Exponential decay rate
  minEngagementLevel: 0.1                    // Below this = timeout
}
```

## Behavior Examples

### Active Conversation (0-2 minutes inactive)
- Engagement: 100%
- Delay Multiplier: 1.0x (normal speed)
- Status: "ACTIVE (1m inactive, 100% engaged, normal speed)"

### Light Decay (3 minutes inactive)
- Engagement: 75%
- Delay Multiplier: 1.8x
- Status: "LIGHT_DECAY (3m inactive, 75% engaged, 1.8x slower)"

### Heavy Decay (12 minutes inactive)
- Engagement: 15%
- Delay Multiplier: 6.2x
- Status: "HEAVY_DECAY (12m inactive, 15% engaged, 6.2x slower)"

### Timeout (16 minutes inactive)
- Engagement: 0%
- Delay Multiplier: ∞
- Status: "TIMEOUT (16m inactive, 0% engaged)"
- All character interactions stop

## User Experience

1. **Immediate Response**: When user sends a message, engagement resets to 100%
2. **Gradual Slowdown**: Characters respond progressively slower as user becomes inactive
3. **Natural Fade**: Conversations naturally wind down rather than abruptly stopping
4. **Clean Timeout**: After 15 minutes of inactivity, conversation ends gracefully
5. **Activity Reset**: Any user message immediately restores normal timing

## Logging

The system provides detailed logging for monitoring decay behavior:
- Engagement level calculations
- Delay multiplier applications
- Conversation timeout events
- Character interaction scheduling with decay status

## Testing

Use `test_decay_functionality.js` to verify the decay system:
```bash
node test_decay_functionality.js
```

This test script:
1. Starts a conversation
2. Waits 3 minutes to observe moderate decay
3. Waits 8 minutes to observe heavy decay  
4. Waits 16 minutes to observe conversation timeout

## Benefits

- **Natural Conversation Flow**: Mimics how real conversations naturally slow down
- **Resource Efficiency**: Reduces server load for inactive conversations
- **Better User Experience**: Avoids overwhelming inactive users with messages
- **Graceful Degradation**: Smooth transition from active to inactive states
- **Configurable**: Easy to adjust timing and decay rates based on usage patterns
