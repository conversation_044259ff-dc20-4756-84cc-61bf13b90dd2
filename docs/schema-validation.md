# LLM Response Schema Validation

## Overview

The ForaChat application now includes JSON schema validation for LLM responses to ensure consistent and correctly formatted output from the Gemini API.

## Implementation

### Schema Definition

The response schema is defined in `prompts/output.json` and specifies the expected structure for LLM responses:

```json
{
  "type": "object",
  "properties": {
    "reply": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "character": { "type": "string" },
          "delay": { 
            "type": "integer",
            "description": "Delay in milliseconds before showing this message"
          },
          "text": { "type": "string" }
        },
        "required": ["character", "text", "delay"],
        "propertyOrdering": ["character", "text", "delay"]
      }
    },
    "skills": {
      "type": "array",
      "items": { "type": "string" }
    },
    "theme": { "type": "string" }
  },
  "required": ["reply", "skills", "theme"],
  "propertyOrdering": ["reply", "skills", "theme"]
}
```

### GeminiLLMService Changes

The `GeminiLLMService` has been enhanced with:

1. **Schema Loading**: Automatically loads the schema from `prompts/output.json`
2. **Schema Conversion**: Converts JSON Schema format to Gemini API format (lowercase to uppercase types)
3. **Schema Caching**: Caches the converted schema to avoid repeated file reads
4. **Enhanced Logging**: Provides detailed logging about schema loading and validation

### Key Features

- **Structured Output**: The Gemini API now uses `responseSchema` to constrain output format
- **Required Fields**: All essential fields are marked as required to prevent missing data
- **Property Ordering**: Ensures consistent property ordering in responses
- **Fallback Handling**: Gracefully falls back to unstructured JSON if schema loading fails
- **Validation**: Both schema-level and application-level validation for robust error handling

### Benefits

1. **Consistency**: Ensures all LLM responses follow the exact same structure
2. **Reliability**: Reduces parsing errors and malformed responses
3. **Performance**: Schema validation happens at the API level, reducing post-processing
4. **Maintainability**: Schema is defined in a single file and can be easily updated
5. **Debugging**: Enhanced logging helps identify and resolve schema-related issues

### Usage

The schema validation is automatically applied to all LLM requests. No changes are required in calling code - the `GeminiLLMService.generate()` method continues to work as before but now returns more reliable, structured responses.

### Monitoring

The service logs the following events:
- Schema loading success/failure
- Schema conversion completion
- Whether structured output is being used
- Response validation results

Check the application logs for messages like:
- `"Loaded and converted response schema from prompts/output.json"`
- `"Using response schema for structured output"`
- `"LLM response successfully validated against schema"`
