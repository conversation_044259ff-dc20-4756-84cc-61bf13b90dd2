exports.up = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .alterTable('conversations', (table) => {
        table.timestamp('last_user_activity').nullable();
        table.decimal('engagement_level', 3, 2).defaultTo(1.0);
        
        // Add index for performance on last_user_activity queries
        table.index('last_user_activity', 'idx_conversations_last_user_activity');
    })
    .then(() => {
        // Update existing conversations to have initial values
        return knex('forachat.conversations')
            .whereNull('last_user_activity')
            .update({
                last_user_activity: knex.raw('updated_at'),
                engagement_level: 1.0
            });
    });
};

exports.down = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .alterTable('conversations', (table) => {
        table.dropIndex('last_user_activity', 'idx_conversations_last_user_activity');
        table.dropColumn('last_user_activity');
        table.dropColumn('engagement_level');
    });
};
