exports.up = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .createTable('sessions', (table) => {
        table.string('id', 255).primary(); // UUID or custom session ID
        table.string('user_identifier', 255).notNullable(); // Phone number, user ID, cookie value, etc.
        table.enum('channel', ['web', 'repl', 'sms', 'slack', 'teams']).notNullable();
        table.integer('conversation_id').unsigned().references('id').inTable('forachat.conversations').nullable();
        table.timestamp('last_activity').notNullable().defaultTo(knex.fn.now());
        table.timestamp('expires_at').nullable(); // Optional expiration time
        table.json('metadata').nullable(); // Channel-specific metadata
        table.timestamps(true, true);
        
        // Indexes for performance
        table.index(['user_identifier', 'channel'], 'idx_sessions_user_channel');
        table.index(['conversation_id'], 'idx_sessions_conversation');
        table.index(['last_activity'], 'idx_sessions_last_activity');
        table.index(['expires_at'], 'idx_sessions_expires_at');
        table.index(['channel'], 'idx_sessions_channel');
    });
};

exports.down = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .dropTable('sessions');
};
