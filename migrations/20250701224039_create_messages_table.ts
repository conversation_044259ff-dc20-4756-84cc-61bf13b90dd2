exports.up = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .createTable('messages', (table) => {
        table.increments('id').primary();
        table.string('character', 255).notNullable();
        table.text('text').notNullable();
        table.integer('conversation_id').unsigned().references('id').inTable('forachat.conversations');
        table.timestamps(true, true);
    });
};

exports.down = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .dropTable('messages');
};