exports.up = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .alterTable('conversations', (table) => {
        table.string('theme', 500).nullable();
        table.json('skills').nullable();
    });
};

exports.down = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .alterTable('conversations', (table) => {
        table.dropColumn('theme');
        table.dropColumn('skills');
    });
};
