# forachat
Group text for professional interpersonal skills enrichment

# Product Specification and Requirements: AskFora

## 1. Introduction

This document outlines the product specification and requirements for **AskFora**, a platform designed to be the ultimate career companion for the next generation of talent. AskFora is a squad of AI coworker agents, each with their own vibe and specialty, who connect with users directly through **text messaging (SMS)**. This mobile-native approach ensures AskFora is as easy to talk to as a friend.

All communication is managed by a sophisticated back-end, with an API and a simple client UI built exclusively for demonstration and testing purposes. The core product experience lives in the user's native messaging app.

## 2. Vision and Opportunity

**Vision:** To be the go-to confidante for every student, intern, and new grad, giving them the confidence and know-how to crush their early career goals, all through a simple text.

**Opportunity:** Entering the workforce is a whole vibe shift. Gen Z expects instant, authentic, and frictionless solutions. By building AskFora on an SMS platform like Twilio, we eliminate the need for another app download and meet users on a platform they use constantly. It’s the mentor you can text at 10 PM when you're stressing about a deadline, with the immediacy and privacy of a personal conversation.

## 3. Target Audience

Our primary audience is **Gen <PERSON> and younger millennials (ages 18-24)** who are at the very beginning of their professional journey. This includes:

*   **College students** looking for and working at internships.
*   **New graduates** navigating their first full-time job.
*   **Young freelancers and creators** figuring out how to work with clients.

They live on their phones, communicate through text, and value authenticity and immediate peer-to-peer support.

## 4. User Personas

*   **Alex (The Ambitious Professional):** 28 years old, a software engineer recently promoted to a team lead. <PERSON> is struggling with the transition from individual contributor to manager and needs advice on delegation, providing feedback, and motivating their team.
*   **Ben (The Job Seeker):** 25 years old, a marketing associate looking to move to a new company for better growth opportunities. Ben needs help refining his resume, preparing for interviews, and negotiating a competitive salary.
*   **Chloe (The Conflict-Avoider):** 22 years old, a graphic designer who finds it difficult to handle disagreements with stakeholders about creative direction. Chloe needs strategies for de-escalating conflict and advocating for her ideas constructively.

## 5. AI Agent Personas

The success of AskFora hinges on the believability and effectiveness of its AI agents. Each agent will have a distinct personality and communication style, grounded in a backstory that informs their expertise.

### 5.1. The Core Team (Group Chat)

The primary interaction channel is a group chat with Fora, Jan, and Lou, designed to simulate a supportive and well-rounded team.

*   **Fora (The Work Bestie):**
    *   **Personality:** An older Gen Z (late 20s), Fora is the user's initial point of contact. She's approachable, empathetic, and tech-savvy, with a few years of management experience. She uses a slightly informal but professional tone, incorporating occasional emojis and modern workplace slang where appropriate. Fora is adept at understanding the user's initial query and bringing in the right specialists.
    *   **Specialization:** Getting the vibe, figuring out the *actual* problem, and pulling in the right people. Great for general "help me adult" at work questions.
    *   **Example Dialogue:** Fora: omg heyyy welcome! 👋 So glad you're here. Starting a new role is NUTS, I totally get it. Don't even stress. I've already pinged Jan and Lou, my go-to's for this stuff. Lou, you're the master of first impressions, what's your take? 👀
*   **Jan (The Pragmatist):**
    *   **Personality:** Jan is the "real talk" friend. She keeps it 100, always. Her style is short, punchy, and uses a lot of bullet points or numbered lists that are easy to read in a text.
    *   **Specialization:** The "how-to" stuff. Resumes, applications, understanding corporate jargon, and making a plan.
    *   **Example Dialogue:** Jan: Aight, Fora's right, no stress. But let's get tactical. You feel unheard in meetings. Bet. Here's the play: 1. Prep 1-2 talking points *before* the meeting. 2. DM your main point to the meeting lead beforehand. 3. Use Fora's hype to actually speak up. We can practice.
*   **Lou (The People Person):**
    *   **Personality:** Lou is all about the people side of things. He’s super emotionally intelligent and his advice often comes as a relatable story, broken into a few consecutive texts to feel like a real conversation.
    *   **Specialization:** Interpersonal drama, reading social cues, networking without being cringe, and building confidence.
    *   **Example Dialogue:** Lou: Jan's spitting facts with that plan. 🔥 But also, let's talk about the energy in the room. You said your boss is 'intimidating.' Tbh, I once had a manager who I thought hated me. Turns out she was just stressed and introverted. Maybe we can figure out their communication style?

### 5.2. The Specialists (1:1 Chats)

Users can be directed to or can request to speak with specialized agents for focused conversations.

*   **Nat (The Negotiator):**
    *   **Personality:** Confident, strategic, and persuasive. Nat is an expert in negotiation tactics and market value. Her communication is clear, concise, and empowering.
    *   **Specialization:** Salary negotiation, promotions, and advocating for resources.
*   **Irv (The Interviewer):**
    *   **Personality:** Sharp, insightful, and a great coach. Irv has a deep understanding of the hiring process from both sides of the table. He's skilled at mock interviews and providing actionable feedback. Chill and reassuring, can run mock interviews via text, sending you a question and waiting for your response.
    *   **Specialization:** Interview preparation, behavioral questions, and crafting compelling career narratives.
*   **Ren (The Resume Refiner):**
    *   **Personality:** Detail-oriented, articulate, and aesthetically minded. Ren excels at transforming a list of experiences into a compelling story that catches the eye of recruiters. Asks you to text or email your resume, then provides feedback and suggestions directly in the chat.
    *   **Specialization:** Resume and cover letter writing, LinkedIn profile optimization, and personal branding.
*   **Carl (The Conflict De-escalator):**
    *   **Personality:** Calm, patient, and an expert in non-violent communication. Carl helps users navigate difficult conversations and find mutually agreeable solutions. Guides you through difficult conversations with step-by-step text-based advice.
    *   **Specialization:** Conflict resolution, de-escalation techniques, and managing difficult colleagues.

## 6. The Supervising Agent

The Supervising Agent is the "brain" behind the AskFora platform. It operates in the background and is not directly addressable by the user. Its key responsibilities include:

*   **Contextual Understanding:** Analyzes the user's initial query and ongoing conversation to identify the core issue and desired outcome.
*   **Scenario Generation:** Proactively suggests relevant scenarios or topics for discussion based on the user's profile and conversation history (e.g., "It sounds like you're preparing for a performance review. Would you like to role-play the conversation?").
*   **Agent Orchestration:** Determines which agent is best suited to respond at any given moment in the group chat. It provides each agent with the necessary context, grounding information, and "guardrails" to ensure their response is in character and helpful.
*   **Memory Management:** Maintains a persistent memory of the user's goals, past conversations, and key details to ensure a continuous and personalized experience.

# 6. Functional Requirements

| ID | Requirement | Description | Priority |
|---|---|---|---|
| FR-001 | User Onboarding | Onboarding is triggered by a user texting a keyword (e.g., "START") to the AskFora phone number for the first time. Fora initiates the welcome conversation. | Must-have |
| FR-002 | Initial Contact with Fora | Users can hit up Fora directly, or a friend can send them an invite link that drops them into the chat. | Must-have |
| FR-003 | Simulated Group Chat | In the main chat, messages from agents are prefixed with their names to simulate a group conversation over SMS. | Must-have |
| FR-004 | 1:1 Conversations | The system can switch the user's context to a 1:1 conversation with a specialist agent upon request. In this mode, the name prefix is dropped. | Must-have |
| FR-005 | Agent "Chime-in" | The Supervising Agent intelligently processes user texts and triggers the appropriate agent to contribute to the conversation. | Must-have |
| FR-006 | API Access (Internal) | A RESTful API for programmatic interaction, to be used for demos, testing, and potential future integrations ONLY. | High |
| FR-007 | Client UI (Internal) | A web-based client UI for testing and demonstration purposes ONLY, which visualizes the SMS conversations and the Supervising Agent's logic. | High |
| FR-008 | Conversation History | Users have a conversation history by default in their native messaging app. The system must maintain its own back-end log for context and memory. | Must-have |
| FR-009 | CLI (Internal) | A REPL cli for testing and demonstration purposes ONLY, which visualizes the SMS conversations and the Supervising Agent's logic. | High |
| FR-010 | Typing Indicators & Reactions | Show when agents are "typing..." and allow users to react to messages with emojis (👍,🔥,😂). | Medium |
| FR-011 | SMS Platform Integration | The platform must fully integrate with an SMS provider like Twilio for sending and receiving text and MMS messages. This is the core infrastructure. | Must-have |
| FR-012 | Command-Based Navigation | Users can navigate the service using simple text commands (e.g., HELP, SWITCH, TALK TO REN, STOP). | Must-have |

# 7. Non-Functional Requirements

| ID | Requirement | Description |
|---|---|---|
| NFR-001 | Performance | SMS responses should be delivered within 5-10 seconds of receiving the user's message to feel conversational and not automated. |
| NFR-002 | Scalability | The SMS infrastructure must scale to handle high volumes of inbound and outbound messages without delay. |
| NFR-003 | Availability | The service must be online 24/7 with 99.9% uptime to respond to user texts at any time. |
| NFR-004 | Security & Privacy | User phone numbers and conversation content must be encrypted and stored securely. Anonymity and privacy are paramount. |
| NFR-005 | Usability | The experience must be intuitive for anyone who knows how to text. The commands should be simple and forgiving of typos. |

# 8. API & Client UI (For Demos & Testing)

The API and Client UI are internal tools and not part of the core user-facing product. Their purpose is to:

- **API:** Provide endpoints for sending messages as a user to a specific conversation to test agent responses without needing a physical phone.
- **Client UI:** Visualize the back-and-forth of SMS conversations in a more readable format for demos. It should include a "dev view" that shows the Supervising Agent's real-time logic (e.g., "User message triggered 'Interview' intent. Routing to Irv.").

The API will be designed for simplicity and speed.

*   POST /v1/conversations: Start a new chat.
*   GET /v1/conversations/{conversation_id}: Get a chat's message history.
*   POST /v1/conversations/{conversation_id}/messages: Send a message.
*   GET /v1/agents: Get the roster of the AI crew.
*   POST /v1/conversations/{conversation_id}/reactions: React to a message.

A web-based client that looks slick on both mobile and desktop. It will feature:

*   Simple login.
*   A main chat list.
*   The chat interface itself, complete with avatars, reactions, and typing indicators.
*   A "dev view" to show off the Supervising Agent's real-time decision-making.

A cli for testing interactive and background tasks that runs as a repl or with one shot arguments.

# 9. Success Metrics and KPIs

We'll know we're winning if:

## User Engagement
- Daily/Monthly Active Users (defined by sending at least one message).
- Number of messages per user per week.
- High usage of command-based features (e.g., switching to specialists).

## User Love
- Positive qualitative feedback and organic social media mentions.
- Low "STOP" command rate (low churn).

## Retention
- Strong D1/D7/D30 retention. Users should continue to engage with the service over multiple weeks.

# 10. Future Considerations

- **RCS/iMessage Integration:** Explore richer messaging protocols beyond standard SMS/MMS.
- **Proactive Messaging:** With user permission, send proactive check-ins or tips. (e.g., "Hey, you have that big presentation tomorrow, right? Wanna do a quick hype session?").
- **Voice Calls:** Allow users to escalate to an AI-powered voice call with an agent for more complex issues.

# System Architect

The system is in typescript and the UI is in React

The primary LLM is Gemini flash 2.5
All LLM outputs follow the format in prompts/output.json
System Prompts are in prompts/

Source files are in src/
Build output is in dist/

## Development

### REPL (Command Line Interface)

The ForaChat REPL provides a command-line interface for testing and development:

```bash
# Connect to existing server (default: localhost:3000)
npm run repl

# Start server locally and connect
npm run repl -- --local

# Resume a specific session
npm run repl -- --session-id <session-id>

# View all options
npm run repl -- --help
```

**Note:** Use `--` to pass options to the REPL script when using npm.

