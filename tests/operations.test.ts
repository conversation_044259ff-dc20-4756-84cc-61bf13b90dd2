import { ForaChat } from '../src/operations';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { getMockResponse } from './mocks/llmResponses';

// The services are already mocked in setup.ts

// Import the mocked services
import { ConversationService } from '../src/core/ConversationService';
import { PromptService } from '../src/core/PromptService';
import { GeminiLLMService } from '../src/services/GeminiLLMService';

const mockConversationService = ConversationService as jest.Mocked<typeof ConversationService>;
const mockPromptService = PromptService as jest.Mocked<typeof PromptService>;
const mockForaChat = ForaChat as jest.Mocked<typeof ForaChat>;

describe('ForaChat', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock system prompt
    mockPromptService.getSystemPrompt.mockResolvedValue('Mock system prompt content');
  });

  describe('createConversation', () => {
    it('should create a new conversation', async () => {
      const expectedConversation = {
        id: 1,
        created_at: new Date(),
        updated_at: new Date(),
        theme: undefined,
        skills: undefined,
        last_user_activity: new Date(),
        engagement_level: 1.0
      };

      mockForaChat.createConversation.mockResolvedValue(expectedConversation);

      const result = await ForaChat.createConversation();

      expect(mockForaChat.createConversation).toHaveBeenCalled();
      expect(result).toEqual(expectedConversation);
    });
  });

  describe('addMessage', () => {
    it('should add a message to a conversation', async () => {
      const expectedMessage = {
        id: 1,
        character: 'user',
        text: 'Hello world',
        conversation_id: 1,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockForaChat.addMessage.mockResolvedValue(expectedMessage);

      const result = await ForaChat.addMessage('user', 'Hello world', 1);

      expect(mockForaChat.addMessage).toHaveBeenCalledWith('user', 'Hello world', 1);
      expect(result).toEqual(expectedMessage);
    });
  });

  describe('getSystemPrompt', () => {
    it('should read system prompt from file', async () => {
      const expectedPrompt = 'System prompt content';
      mockForaChat.getSystemPrompt.mockResolvedValue(expectedPrompt);

      const result = await ForaChat.getSystemPrompt();

      expect(mockForaChat.getSystemPrompt).toHaveBeenCalled();
      expect(result).toBe(expectedPrompt);
    });
  });

  describe('chatWorkflow', () => {
    it('should complete full chat workflow for communication query', async () => {
      const userMessage = 'How can I communicate better?';
      const mockResponse = getMockResponse(userMessage);
      const expectedResult = { ...mockResponse, conversationId: 1 };

      mockForaChat.chatWorkflow.mockResolvedValue(expectedResult);

      const result = await ForaChat.chatWorkflow(userMessage);

      expect(mockForaChat.chatWorkflow).toHaveBeenCalledWith(userMessage);
      expect(result).toEqual(expectedResult);
      expect(result.reply).toHaveLength(4);
      expect(result.skills).toContain('clear communication');
      expect(result.theme).toBe('Effective Communication');
    });

    it('should handle invalid LLM response structure', async () => {
      const userMessage = 'Test message';

      mockForaChat.chatWorkflow.mockRejectedValue(new Error('Invalid response from LLM - missing or invalid reply array'));

      await expect(ForaChat.chatWorkflow(userMessage))
        .rejects.toThrow('Invalid response from LLM - missing or invalid reply array');
    });

    it('should handle empty reply array', async () => {
      const userMessage = 'Test message';
      const expectedResult = {
        reply: [{
          character: 'Fora',
          text: 'I focus on interpersonal workplace skills. Could you clarify what specific area you need help with?',
          delay: 1000
        }],
        theme: 'Request clarification needed',
        skills: [],
        conversationId: 1
      };

      mockForaChat.chatWorkflow.mockResolvedValue(expectedResult);

      const result = await ForaChat.chatWorkflow(userMessage);

      // Should add a helpful response when reply is empty
      expect(result.reply).toHaveLength(1);
      expect(result.reply[0].character).toBe('Fora');
      expect(result.reply[0].text).toContain('interpersonal workplace skills');
      expect(result.theme).toBe('Request clarification needed');
    });

    it('should handle null LLM response', async () => {
      const userMessage = 'Test message';

      mockForaChat.chatWorkflow.mockRejectedValue(new Error('Invalid response from LLM - missing or invalid reply array'));

      await expect(ForaChat.chatWorkflow(userMessage))
        .rejects.toThrow('Invalid response from LLM - missing or invalid reply array');
    });
  });
});
