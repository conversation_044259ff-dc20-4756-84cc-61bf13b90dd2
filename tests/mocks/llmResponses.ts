// Mock LLM responses for consistent testing

export const mockLLMResponses = {
  communicationSkills: {
    reply: [
      {
        character: "Fora",
        text: "Hey! 👋 Sounds like you need some communication tips! Let me get the crew together 💪",
        delay: 1000
      },
      {
        character: "<PERSON>",
        text: "Clear communication = clear results. Start with your main point first, then add details 📊",
        delay: 2000
      },
      {
        character: "<PERSON>",
        text: "Also remember to read the room! 👀 Body language tells you if people are following along ✨",
        delay: 1500
      },
      {
        character: "Fora",
        text: "Exactly! And don't forget to ask questions to make sure everyone's on the same page 🤝",
        delay: 1800
      }
    ],
    skills: ["clear communication", "active listening", "engagement"],
    theme: "Effective Communication"
  },

  conflictResolution: {
    reply: [
      {
        character: "Jan",
        text: "Conflict at work? Time for some structured problem-solving 🎯",
        delay: 1000
      },
      {
        character: "<PERSON>",
        text: "First rule: separate the person from the problem. Focus on behaviors, not personalities 🧠",
        delay: 2200
      },
      {
        character: "Fora",
        text: "Yes! And create a safe space for everyone to share their perspective 🌟",
        delay: 1600
      },
      {
        character: "<PERSON>",
        text: "Document everything. Facts > feelings when resolving workplace issues 📝",
        delay: 1400
      }
    ],
    skills: ["conflict resolution", "mediation", "emotional intelligence"],
    theme: "Workplace Conflict Resolution"
  },

  leadership: {
    reply: [
      {
        character: "Fora",
        text: "Leadership vibes incoming! 🚀 What specific leadership challenge are you facing?",
        delay: 1000
      },
      {
        character: "Lou",
        text: "Remember: leadership isn't about being the loudest in the room 🎭 It's about lifting others up",
        delay: 2000
      },
      {
        character: "Jan",
        text: "Set clear expectations, provide resources, then get out of their way 📈",
        delay: 1800
      },
      {
        character: "Fora",
        text: "And celebrate wins! Recognition costs nothing but means everything 🎉",
        delay: 1500
      }
    ],
    skills: ["leadership", "team building", "motivation"],
    theme: "Leadership Development"
  },

  teamwork: {
    reply: [
      {
        character: "Lou",
        text: "Teamwork makes the dream work! 🤝 But it takes intentional effort",
        delay: 1000
      },
      {
        character: "Jan",
        text: "Define roles clearly. Confusion kills collaboration 🎯",
        delay: 1700
      },
      {
        character: "Fora",
        text: "And create psychological safety! People need to feel safe to share ideas 💡",
        delay: 1900
      },
      {
        character: "Lou",
        text: "Regular check-ins keep everyone aligned. Don't wait for problems to surface 📅",
        delay: 1600
      }
    ],
    skills: ["teamwork", "collaboration", "trust"],
    theme: "Team Collaboration"
  },

  feedback: {
    reply: [
      {
        character: "Jan",
        text: "Feedback time! 📊 The key is being specific and actionable",
        delay: 1000
      },
      {
        character: "Fora",
        text: "Start with something positive, then address the growth area 🌱",
        delay: 1800
      },
      {
        character: "Lou",
        text: "Make it about the work, not the person. 'This approach didn't work' vs 'You're bad at this' 🎯",
        delay: 2100
      },
      {
        character: "Jan",
        text: "End with next steps. Feedback without action items is just complaining 📝",
        delay: 1500
      }
    ],
    skills: ["feedback", "communication", "professional growth"],
    theme: "Giving Effective Feedback"
  },

  invalidRequest: {
    reply: [
      {
        character: "Fora",
        text: "Hmm, that doesn't seem like a workplace skills question 🤔",
        delay: 1000
      },
      {
        character: "Jan",
        text: "We focus on interpersonal professional skills. Try asking about communication, leadership, or teamwork! 💼",
        delay: 2000
      }
    ],
    skills: [],
    theme: "Request Clarification"
  }
};

export const getMockResponse = (userMessage: string) => {
  const message = userMessage.toLowerCase();

  // More specific conflict detection first
  if (message.includes('conflict') || message.includes('arguing') || message.includes('disagreement')) {
    return mockLLMResponses.conflictResolution;
  }

  // Communication skills detection
  if (message.includes('communication') || message.includes('communicate') ||
      message.includes('meeting') || message.includes('clearly')) {
    return mockLLMResponses.communicationSkills;
  }

  // Leadership detection
  if (message.includes('leadership') || message.includes('leader') ||
      message.includes('lead') || message.includes('promoted') ||
      message.includes('manage')) {
    return mockLLMResponses.leadership;
  }

  // Feedback detection
  if (message.includes('feedback') || message.includes('review') ||
      message.includes('direct report')) {
    return mockLLMResponses.feedback;
  }

  // Team collaboration detection (but not conflict)
  if (message.includes('team') || message.includes('collaborate')) {
    return mockLLMResponses.teamwork;
  }

  // Default to communication skills for workplace-related queries
  if (message.includes('work') || message.includes('office') ||
      message.includes('colleague') || message.includes('professional')) {
    return mockLLMResponses.communicationSkills;
  }

  return mockLLMResponses.invalidRequest;
};
