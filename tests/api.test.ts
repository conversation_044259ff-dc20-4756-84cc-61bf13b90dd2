import request from 'supertest';
import express from 'express';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { ForaChat } from '../src/operations';
import { getMockResponse } from './mocks/llmResponses';

// Create a test app similar to the main app
const createTestApp = () => {
  const app = express();
  app.use(express.json());

  app.post('/chat', (req, res) => {
    (async () => {
      try {
        const { text } = req.body;
        if (!text) {
          return res.status(400).json({ error: 'Request body must include "text"' });
        }
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text);
        const result = await handle.getResult();
        res.json(result);
      } catch (error) {
        const errorMessage = (error as Error).message;
        res.status(500).json({ error: "An internal server error occurred.", details: errorMessage });
      }
    })();
  });

  return app;
};

// Mock DBOS workflow
const mockWorkflowHandle = {
  getResult: jest.fn(),
};

const mockStartWorkflow = jest.fn().mockReturnValue({
  chatWorkflow: jest.fn().mockResolvedValue(mockWorkflowHandle),
});

(DBOS.startWorkflow as jest.Mock).mockImplementation(mockStartWorkflow);

describe('API Endpoints', () => {
  let app: express.Application;

  beforeEach(() => {
    jest.clearAllMocks();
    app = createTestApp();
  });

  describe('POST /chat', () => {
    it('should return successful response for valid communication query', async () => {
      const userMessage = 'How can I communicate better with my team?';
      const expectedResponse = getMockResponse(userMessage);
      
      mockWorkflowHandle.getResult.mockResolvedValue(expectedResponse);

      const response = await request(app)
        .post('/chat')
        .send({ text: userMessage })
        .expect(200);

      expect(response.body).toEqual(expectedResponse);
      expect(response.body.reply).toHaveLength(4);
      expect(response.body.skills).toContain('clear communication');
      expect(response.body.theme).toBe('Effective Communication');
    });

    it('should return successful response for conflict resolution query', async () => {
      const userMessage = 'There is conflict in my team';
      const expectedResponse = getMockResponse(userMessage);
      
      mockWorkflowHandle.getResult.mockResolvedValue(expectedResponse);

      const response = await request(app)
        .post('/chat')
        .send({ text: userMessage })
        .expect(200);

      expect(response.body).toEqual(expectedResponse);
      expect(response.body.skills).toContain('conflict resolution');
      expect(response.body.theme).toBe('Workplace Conflict Resolution');
    });

    it('should return successful response for leadership query', async () => {
      const userMessage = 'How can I be a better leader?';
      const expectedResponse = getMockResponse(userMessage);
      
      mockWorkflowHandle.getResult.mockResolvedValue(expectedResponse);

      const response = await request(app)
        .post('/chat')
        .send({ text: userMessage })
        .expect(200);

      expect(response.body).toEqual(expectedResponse);
      expect(response.body.skills).toContain('leadership');
      expect(response.body.theme).toBe('Leadership Development');
    });

    it('should return 400 for missing text field', async () => {
      const response = await request(app)
        .post('/chat')
        .send({})
        .expect(400);

      expect(response.body).toEqual({
        error: 'Request body must include "text"'
      });
    });

    it('should return 400 for empty text field', async () => {
      const response = await request(app)
        .post('/chat')
        .send({ text: '' })
        .expect(400);

      expect(response.body).toEqual({
        error: 'Request body must include "text"'
      });
    });

    it('should return 500 for workflow errors', async () => {
      const userMessage = 'Test message';
      const errorMessage = 'Workflow failed';
      
      mockWorkflowHandle.getResult.mockRejectedValue(new Error(errorMessage));

      const response = await request(app)
        .post('/chat')
        .send({ text: userMessage })
        .expect(500);

      expect(response.body).toEqual({
        error: 'An internal server error occurred.',
        details: errorMessage
      });
    });

    it('should handle malformed JSON in request body', async () => {
      const response = await request(app)
        .post('/chat')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      // Express will handle malformed JSON and return a 400 error
      expect(response.status).toBe(400);
    });

    it('should verify DBOS workflow is called correctly', async () => {
      const userMessage = 'Test workflow call';
      const expectedResponse = getMockResponse(userMessage);
      
      mockWorkflowHandle.getResult.mockResolvedValue(expectedResponse);

      await request(app)
        .post('/chat')
        .send({ text: userMessage })
        .expect(200);

      expect(DBOS.startWorkflow).toHaveBeenCalledWith(ForaChat);
      expect(mockStartWorkflow().chatWorkflow).toHaveBeenCalledWith(userMessage);
      expect(mockWorkflowHandle.getResult).toHaveBeenCalled();
    });
  });
});
