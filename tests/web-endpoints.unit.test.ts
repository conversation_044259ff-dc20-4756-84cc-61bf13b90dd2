import request from 'supertest';
import express from 'express';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { WebInterface } from '../src/interfaces/WebInterface';

// Mock DBOS startWorkflow for unit testing
const mockWorkflowResults = new Map();

const mockDBOS = {
  startWorkflow: jest.fn().mockImplementation((operationsClass) => {
    const workflowMethods: any = {};
    
    // Create mock workflow methods that return predefined results
    const createMockMethod = (methodName: string) => {
      return jest.fn().mockImplementation(async (...args: any[]) => ({
        getResult: jest.fn().mockImplementation(async () => {
          const key = `${methodName}_${JSON.stringify(args)}`;
          return mockWorkflowResults.get(key) || mockWorkflowResults.get(methodName);
        })
      }));
    };

    // Session management methods
    workflowMethods.createSession = createMockMethod('createSession');
    workflowMethods.getSession = createMockMethod('getSession');
    workflowMethods.deleteSession = createMockMethod('deleteSession');
    workflowMethods.extendSession = createMockMethod('extendSession');
    workflowMethods.updateSessionActivity = createMockMethod('updateSessionActivity');
    workflowMethods.updateSessionConversation = createMockMethod('updateSessionConversation');
    workflowMethods.getSessionsByUser = createMockMethod('getSessionsByUser');
    workflowMethods.cleanupExpiredSessions = createMockMethod('cleanupExpiredSessions');
    
    // Conversation methods
    workflowMethods.getConversationBySession = createMockMethod('getConversationBySession');
    workflowMethods.getConversationMessages = createMockMethod('getConversationMessages');
    workflowMethods.getActiveSessionsForUser = createMockMethod('getActiveSessionsForUser');
    
    // Cleanup methods
    workflowMethods.getCleanupStats = createMockMethod('getCleanupStats');
    workflowMethods.manualSessionCleanup = createMockMethod('manualSessionCleanup');
    
    // Chat methods
    workflowMethods.chatWorkflow = createMockMethod('chatWorkflow');

    return workflowMethods;
  }),
  logger: {
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
};

// Replace DBOS with our mock
(DBOS as any).startWorkflow = mockDBOS.startWorkflow;
(DBOS as any).logger = mockDBOS.logger;

describe('Web Endpoints Unit Tests', () => {
  let app: express.Application;
  let webInterface: WebInterface;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();
    mockWorkflowResults.clear();

    // Create mock ChatService
    const mockChatService = {
      processMessage: jest.fn(),
      getConversationHistory: jest.fn(),
    } as any;

    // Create WebInterface instance
    webInterface = new WebInterface(mockChatService);
    app = webInterface.getApp();
  });

  describe('Session API Endpoints', () => {
    test('POST /api/session should create a new session', async () => {
      const sessionRequest = {
        userIdentifier: 'test_user',
        channel: 'web',
        metadata: { test: 'data' }
      };

      const mockSession = {
        id: 'session-123',
        user_identifier: 'test_user',
        channel: 'web',
        created_at: new Date(),
        updated_at: new Date(),
        last_activity: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
        metadata: { test: 'data' },
        conversation_id: null
      };

      mockWorkflowResults.set('createSession', mockSession);

      const response = await request(app)
        .post('/api/session')
        .send(sessionRequest)
        .expect(200);

      expect(response.body.sessionId).toBe('session-123');
      expect(response.body.channel).toBe('web');
      expect(mockDBOS.startWorkflow).toHaveBeenCalled();
    });

    test('POST /api/session should return 400 for invalid request', async () => {
      const invalidRequest = {
        userIdentifier: 'test_user'
        // Missing required 'channel' field
      };

      const response = await request(app)
        .post('/api/session')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.error).toContain('userIdentifier and channel are required');
    });

    test('GET /api/session/:id should return session info', async () => {
      const mockSession = {
        id: 'session-123',
        user_identifier: 'test_user',
        channel: 'web',
        created_at: new Date(),
        updated_at: new Date(),
        last_activity: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
        metadata: {},
        conversation_id: null
      };

      mockWorkflowResults.set('getSession', mockSession);

      const response = await request(app)
        .get('/api/session/session-123')
        .expect(200);

      expect(response.body.id).toBe('session-123');
      expect(response.body.user_identifier).toBe('test_user');
    });

    test('GET /api/session/:id should return 404 for non-existent session', async () => {
      mockWorkflowResults.set('getSession', null);

      const response = await request(app)
        .get('/api/session/non-existent')
        .expect(404);

      expect(response.body.error).toBe('Session not found');
    });

    test('DELETE /api/session/:id should delete session', async () => {
      // Mock getSession to return a session first
      const mockSession = {
        id: 'session-123',
        user_identifier: 'test_user',
        channel: 'web',
        created_at: new Date(),
        updated_at: new Date(),
        last_activity: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
        metadata: {},
        conversation_id: null
      };

      mockWorkflowResults.set('getSession', mockSession);
      mockWorkflowResults.set('deleteSession', undefined);

      const response = await request(app)
        .delete('/api/session/session-123')
        .expect(200);

      expect(response.body.message).toBe('Session deleted successfully');
    });

    test('DELETE /api/session/:id should return 404 for non-existent session', async () => {
      mockWorkflowResults.set('getSession', null);

      const response = await request(app)
        .delete('/api/session/non-existent')
        .expect(404);

      expect(response.body.error).toBe('Session not found');
    });

    test('PUT /api/session/:id/extend should extend session', async () => {
      const mockSession = {
        id: 'session-123',
        user_identifier: 'test_user',
        channel: 'web',
        created_at: new Date(),
        updated_at: new Date(),
        last_activity: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
        metadata: {},
        conversation_id: null
      };

      mockWorkflowResults.set('getSession', mockSession);
      mockWorkflowResults.set('extendSession', undefined);

      const response = await request(app)
        .put('/api/session/session-123/extend')
        .send({ hours: 48 })
        .expect(200);

      expect(response.body.message).toBe('Session extended successfully');
    });
  });

  describe('Conversation API Endpoints', () => {
    test('GET /api/session/:id/conversation should return session conversation', async () => {
      const mockSession = {
        id: 'session-123',
        conversation_id: 456
      };

      const mockMessages = [
        { id: 1, character: 'user', text: 'Hello', created_at: new Date() },
        { id: 2, character: 'Fora', text: 'Hi there!', created_at: new Date() }
      ];

      mockWorkflowResults.set('getSession', mockSession);
      mockWorkflowResults.set('getConversationMessages', mockMessages);

      const response = await request(app)
        .get('/api/session/session-123/conversation')
        .expect(200);

      expect(response.body.conversationId).toBe(456);
      expect(response.body.messages).toHaveLength(2);
    });

    test('GET /api/session/:id/conversation should return 404 for session without conversation', async () => {
      const mockSession = {
        id: 'session-123',
        conversation_id: null
      };

      mockWorkflowResults.set('getSession', mockSession);

      const response = await request(app)
        .get('/api/session/session-123/conversation')
        .expect(404);

      expect(response.body.error).toBe('No conversation found for this session');
    });
  });

  describe('User Sessions API', () => {
    test('GET /api/user/:identifier/sessions should return user sessions', async () => {
      const mockSessions = [
        { id: 'session-1', channel: 'web', created_at: new Date() },
        { id: 'session-2', channel: 'repl', created_at: new Date() }
      ];

      mockWorkflowResults.set('getActiveSessionsForUser', mockSessions);

      const response = await request(app)
        .get('/api/user/test_user/sessions')
        .expect(200);

      expect(response.body.userIdentifier).toBe('test_user');
      expect(response.body.sessions).toHaveLength(2);
    });
  });

  describe('Cleanup API Endpoints', () => {
    test('GET /api/sessions/stats should return cleanup statistics', async () => {
      const mockStats = {
        totalSessions: 100,
        activeSessions: 80,
        expiredSessions: 20,
        totalConversations: 50,
        totalMessages: 500
      };

      mockWorkflowResults.set('getCleanupStats', mockStats);

      const response = await request(app)
        .get('/api/sessions/stats')
        .expect(200);

      expect(response.body.totalSessions).toBe(100);
      expect(response.body.activeSessions).toBe(80);
    });

    test('POST /api/sessions/manual-cleanup should perform manual cleanup', async () => {
      const mockResult = {
        expiredSessions: 5,
        orphanedConversations: 2,
        oldMessages: 10
      };

      mockWorkflowResults.set('manualSessionCleanup', mockResult);

      const response = await request(app)
        .post('/api/sessions/manual-cleanup')
        .send({ dryRun: false })
        .expect(200);

      expect(response.body.message).toBe('Manual cleanup completed');
      expect(response.body.expiredSessions).toBe(5);
    });
  });
});
