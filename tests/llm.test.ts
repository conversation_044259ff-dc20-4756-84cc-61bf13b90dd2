import { LLM } from '../src/llm';
import { getMockResponse } from './mocks/llmResponses';

// Mock the LLM module directly
jest.mock('../src/llm', () => ({
  LLM: {
    generate: jest.fn(),
  },
}));

const mockLLM = LLM as jest.Mocked<typeof LLM>;

describe('LLM', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generate', () => {
    it('should generate a response for communication skills query', async () => {
      const userMessage = 'How can I communicate better with my team?';
      const expectedResponse = getMockResponse(userMessage);

      mockLLM.generate.mockResolvedValue(expectedResponse);

      const result = await LLM.generate('system prompt', userMessage);

      expect(result).toEqual(expectedResponse);
      expect(result.reply).toHaveLength(4);
      expect(result.skills).toContain('clear communication');
      expect(result.theme).toBe('Effective Communication');
    });

    it('should generate a response for conflict resolution query', async () => {
      const userMessage = 'There is conflict in my team, how do I resolve it?';
      const expectedResponse = getMockResponse(userMessage);

      mockLLM.generate.mockResolvedValue(expectedResponse);

      const result = await LLM.generate('system prompt', userMessage);

      expect(result).toEqual(expectedResponse);
      expect(result.skills).toContain('conflict resolution');
      expect(result.theme).toBe('Workplace Conflict Resolution');
    });

    it('should generate a response for leadership query', async () => {
      const userMessage = 'How can I be a better leader?';
      const expectedResponse = getMockResponse(userMessage);

      mockLLM.generate.mockResolvedValue(expectedResponse);

      const result = await LLM.generate('system prompt', userMessage);

      expect(result).toEqual(expectedResponse);
      expect(result.skills).toContain('leadership');
      expect(result.theme).toBe('Leadership Development');
    });

    it('should handle invalid JSON response', async () => {
      mockLLM.generate.mockRejectedValue(new Error('Invalid JSON response from LLM: test error'));

      await expect(LLM.generate('system prompt', 'test message'))
        .rejects.toThrow('Invalid JSON response from LLM');
    });

    it('should configure the model correctly', async () => {
      const expectedResponse = getMockResponse('test');

      mockLLM.generate.mockResolvedValue(expectedResponse);

      await LLM.generate('system prompt', 'test message');

      expect(mockLLM.generate).toHaveBeenCalledWith('system prompt', 'test message');
    });

    it('should handle all character types in response', async () => {
      const userMessage = 'teamwork question';
      const expectedResponse = getMockResponse(userMessage);

      mockLLM.generate.mockResolvedValue(expectedResponse);

      const result = await LLM.generate('system prompt', userMessage);

      const characters = result.reply.map((msg: any) => msg.character);
      expect(characters).toContain('Lou');
      expect(characters).toContain('Jan');
      expect(characters).toContain('Fora');
    });
  });
});
