import { ConversationService } from '../../src/core/ConversationService';
import { DBOS } from '@dbos-inc/dbos-sdk';

describe('ConversationService', () => {
  const mockKnexClient = {
    insert: jest.fn(),
    returning: jest.fn(),
    where: jest.fn(),
    orderBy: jest.fn(),
    first: jest.fn(),
    del: jest.fn(),
    limit: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup knex client mock chain
    mockKnexClient.insert.mockReturnValue(mockKnexClient);
    mockKnexClient.returning.mockReturnValue(mockKnexClient);
    mockKnexClient.where.mockReturnValue(mockKnexClient);
    mockKnexClient.orderBy.mockReturnValue(mockKnexClient);
    mockKnexClient.limit.mockReturnValue(mockKnexClient);
    
    (DBOS.knexClient as unknown as jest.Mock).mockReturnValue(mockKnexClient);
  });

  describe('createConversation', () => {
    it('should create a new conversation', async () => {
      const expectedConversation = {
        id: 1,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Since ConversationService is mocked in setup.ts, we just test the mock behavior
      (ConversationService.createConversation as jest.Mock).mockResolvedValue(expectedConversation);

      const result = await ConversationService.createConversation();

      expect(result).toEqual(expectedConversation);
      expect(ConversationService.createConversation).toHaveBeenCalled();
    });
  });

  describe('addMessage', () => {
    it('should add a message to a conversation', async () => {
      const expectedMessage = {
        id: 1,
        character: 'user',
        text: 'Hello world',
        conversation_id: 1,
        created_at: new Date(),
        updated_at: new Date(),
      };

      (ConversationService.addMessage as jest.Mock).mockResolvedValue(expectedMessage);

      const result = await ConversationService.addMessage('user', 'Hello world', 1);

      expect(result).toEqual(expectedMessage);
      expect(ConversationService.addMessage).toHaveBeenCalledWith('user', 'Hello world', 1);
    });
  });

  describe('getConversationMessages', () => {
    it('should retrieve messages for a conversation', async () => {
      const expectedMessages = [
        { id: 1, character: 'user', text: 'Hello', conversation_id: 1, created_at: new Date(), updated_at: new Date() },
        { id: 2, character: 'Fora', text: 'Hi there!', conversation_id: 1, created_at: new Date(), updated_at: new Date() },
      ];

      (ConversationService.getConversationMessages as jest.Mock).mockResolvedValue(expectedMessages);

      const result = await ConversationService.getConversationMessages(1);

      expect(result).toEqual(expectedMessages);
      expect(ConversationService.getConversationMessages).toHaveBeenCalledWith(1);
    });
  });

  describe('getConversation', () => {
    it('should retrieve a conversation by ID', async () => {
      const expectedConversation = {
        id: 1,
        created_at: new Date(),
        updated_at: new Date(),
      };

      (ConversationService.getConversation as jest.Mock).mockResolvedValue(expectedConversation);

      const result = await ConversationService.getConversation(1);

      expect(result).toEqual(expectedConversation);
      expect(ConversationService.getConversation).toHaveBeenCalledWith(1);
    });

    it('should return null for non-existent conversation', async () => {
      (ConversationService.getConversation as jest.Mock).mockResolvedValue(null);

      const result = await ConversationService.getConversation(999);

      expect(result).toBeNull();
      expect(ConversationService.getConversation).toHaveBeenCalledWith(999);
    });
  });

  describe('deleteConversation', () => {
    it('should delete a conversation and its messages', async () => {
      (ConversationService.deleteConversation as jest.Mock).mockResolvedValue(undefined);

      await ConversationService.deleteConversation(1);

      expect(ConversationService.deleteConversation).toHaveBeenCalledWith(1);
    });
  });

  describe('getRecentConversations', () => {
    it('should retrieve recent conversations with default limit', async () => {
      const expectedConversations = [
        { id: 2, created_at: new Date(), updated_at: new Date() },
        { id: 1, created_at: new Date(), updated_at: new Date() },
      ];

      (ConversationService.getRecentConversations as jest.Mock).mockResolvedValue(expectedConversations);

      const result = await ConversationService.getRecentConversations();

      expect(result).toEqual(expectedConversations);
      expect(ConversationService.getRecentConversations).toHaveBeenCalledWith();
    });

    it('should retrieve recent conversations with custom limit', async () => {
      const expectedConversations = [
        { id: 1, created_at: new Date(), updated_at: new Date() },
      ];

      (ConversationService.getRecentConversations as jest.Mock).mockResolvedValue(expectedConversations);

      const result = await ConversationService.getRecentConversations(5);

      expect(result).toEqual(expectedConversations);
      expect(ConversationService.getRecentConversations).toHaveBeenCalledWith(5);
    });
  });
});
