import { ConversationWorkflowService } from '../../src/core/ConversationWorkflowService';
import { ConversationService } from '../../src/core/ConversationService';
import { PromptService } from '../../src/core/PromptService';
import { LLMService } from '../../src/services/LLMService';
import { DBOS } from '@dbos-inc/dbos-sdk';

// Mock the dependencies
jest.mock('../../src/core/ConversationService');
jest.mock('../../src/core/PromptService');
jest.mock('../../src/core/MessageQueueService', () => ({
  MessageQueueService: {
    checkSimilarity: jest.fn().mockResolvedValue({ isDuplicate: false, isSimilar: false }),
  },
}));

describe('ConversationWorkflowService', () => {
  let mockLLMService: jest.Mocked<LLMService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mock LLM service
    mockLLMService = {
      generate: jest.fn(),
    } as jest.Mocked<LLMService>;
    
    // Set the mock LLM service
    ConversationWorkflowService.setLLMService(mockLLMService);
  });

  describe('hasThemeChangedSignificantly', () => {
    it('should return false for identical themes', () => {
      const result = (ConversationWorkflowService as any).hasThemeChangedSignificantly(
        'communication', 
        'communication'
      );
      expect(result).toBe(false);
    });

    it('should return false for themes in the same category', () => {
      const result = (ConversationWorkflowService as any).hasThemeChangedSignificantly(
        'communication', 
        'active listening'
      );
      expect(result).toBe(false);
    });

    it('should return true for themes in different categories', () => {
      const result = (ConversationWorkflowService as any).hasThemeChangedSignificantly(
        'communication', 
        'team building'
      );
      expect(result).toBe(true);
    });

    it('should return false when either theme is empty', () => {
      const result1 = (ConversationWorkflowService as any).hasThemeChangedSignificantly('', 'communication');
      const result2 = (ConversationWorkflowService as any).hasThemeChangedSignificantly('communication', '');
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });
  });

  describe('haveSkillsChangedSignificantly', () => {
    it('should return false when there is skill overlap', () => {
      const currentSkills = ['communication', 'teamwork'];
      const suggestedSkills = ['communication', 'leadership'];
      
      const result = (ConversationWorkflowService as any).haveSkillsChangedSignificantly(
        currentSkills, 
        suggestedSkills
      );
      expect(result).toBe(false);
    });

    it('should return true when there is no skill overlap', () => {
      const currentSkills = ['communication', 'teamwork'];
      const suggestedSkills = ['leadership', 'time management'];
      
      const result = (ConversationWorkflowService as any).haveSkillsChangedSignificantly(
        currentSkills, 
        suggestedSkills
      );
      expect(result).toBe(true);
    });

    it('should return false when either skills list is empty', () => {
      const result1 = (ConversationWorkflowService as any).haveSkillsChangedSignificantly([], ['communication']);
      const result2 = (ConversationWorkflowService as any).haveSkillsChangedSignificantly(['communication'], []);
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });

    it('should handle string skills format', () => {
      const currentSkills = '["communication", "teamwork"]';
      const suggestedSkills = ['communication', 'leadership'];
      
      const result = (ConversationWorkflowService as any).haveSkillsChangedSignificantly(
        currentSkills, 
        suggestedSkills
      );
      expect(result).toBe(false);
    });
  });

  describe('shouldStartNewConversation', () => {
    it('should return true when no current conversation exists', () => {
      const result = ConversationWorkflowService.shouldStartNewConversation(
        null, 
        'communication', 
        ['active listening']
      );
      expect(result).toBe(true);
    });

    it('should return false when theme and skills are similar', () => {
      const currentConversation = {
        theme: 'communication',
        skills: ['active listening', 'feedback']
      };
      
      const result = ConversationWorkflowService.shouldStartNewConversation(
        currentConversation, 
        'feedback', 
        ['active listening', 'difficult conversations']
      );
      expect(result).toBe(false);
    });

    it('should return true when theme changes significantly', () => {
      const currentConversation = {
        theme: 'communication',
        skills: ['active listening']
      };
      
      const result = ConversationWorkflowService.shouldStartNewConversation(
        currentConversation, 
        'team building', 
        ['collaboration']
      );
      expect(result).toBe(true);
    });
  });

  describe('areConversationsSimilar', () => {
    it('should return true for conversations with similar themes and skills', () => {
      const conv1 = { theme: 'communication', skills: ['active listening'] };
      const conv2 = { theme: 'feedback', skills: ['active listening', 'difficult conversations'] };
      
      const result = ConversationWorkflowService.areConversationsSimilar(conv1, conv2);
      expect(result).toBe(true);
    });

    it('should return false for conversations with different themes and skills', () => {
      const conv1 = { theme: 'communication', skills: ['active listening'] };
      const conv2 = { theme: 'team building', skills: ['collaboration'] };
      
      const result = ConversationWorkflowService.areConversationsSimilar(conv1, conv2);
      expect(result).toBe(false);
    });

    it('should return false when either conversation is null', () => {
      const conv1 = { theme: 'communication', skills: ['active listening'] };
      
      const result1 = ConversationWorkflowService.areConversationsSimilar(null, conv1);
      const result2 = ConversationWorkflowService.areConversationsSimilar(conv1, null);
      
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });
  });

  describe('determineConversationRelevance', () => {
    beforeEach(() => {
      // Mock the dependencies
      (ConversationWorkflowService as any).getConversationRelevanceContext = jest.fn();
      (PromptService.getSystemPrompt as jest.Mock).mockResolvedValue('system prompt');
    });

    it('should return false when theme changes significantly', async () => {
      const mockContext = {
        conversation: { theme: 'communication', skills: ['active listening'] },
        recentMessages: [],
        messageCount: 1
      };

      (ConversationWorkflowService as any).getConversationRelevanceContext.mockResolvedValue(mockContext);
      
      mockLLMService.generate.mockResolvedValue({
        reply: [],
        theme: 'team building',
        skills: ['collaboration'],
        isRelated: true,
        reasoning: 'Test reasoning',
        confidence: 80,
        keyFactors: ['test'],
        suggestedTheme: 'team building',
        suggestedSkills: ['collaboration']
      } as any);

      const result = await ConversationWorkflowService.determineConversationRelevance(
        'How do I build better teams?', 
        1
      );

      expect(result).toBe(false);
    });

    it('should return true when theme and skills are similar', async () => {
      const mockContext = {
        conversation: { theme: 'communication', skills: ['active listening'] },
        recentMessages: [],
        messageCount: 1
      };

      (ConversationWorkflowService as any).getConversationRelevanceContext.mockResolvedValue(mockContext);
      
      mockLLMService.generate.mockResolvedValue({
        reply: [],
        theme: 'feedback',
        skills: ['active listening', 'difficult conversations'],
        isRelated: true,
        reasoning: 'Test reasoning',
        confidence: 80,
        keyFactors: ['test'],
        suggestedTheme: 'feedback',
        suggestedSkills: ['active listening', 'difficult conversations']
      } as any);

      const result = await ConversationWorkflowService.determineConversationRelevance(
        'How do I give better feedback?', 
        1
      );

      expect(result).toBe(true);
    });

    it('should return false when LLM determines message is not related', async () => {
      const mockContext = {
        conversation: { theme: 'communication', skills: ['active listening'] },
        recentMessages: [],
        messageCount: 1
      };

      (ConversationWorkflowService as any).getConversationRelevanceContext.mockResolvedValue(mockContext);
      
      mockLLMService.generate.mockResolvedValue({
        reply: [],
        theme: 'time management',
        skills: ['productivity'],
        isRelated: false,
        reasoning: 'Completely different topic',
        confidence: 90,
        keyFactors: ['topic change'],
        suggestedTheme: 'time management',
        suggestedSkills: ['productivity']
      } as any);

      const result = await ConversationWorkflowService.determineConversationRelevance(
        'What time is it?', 
        1
      );

      expect(result).toBe(false);
    });
  });

  describe('chatWorkflow', () => {
    beforeEach(() => {
      // Mock the dependencies
      (ConversationService.createConversation as jest.Mock).mockResolvedValue({ id: 1 });
      (ConversationService.addMessage as jest.Mock).mockResolvedValue({ id: 1 });
      (ConversationService.getConversation as jest.Mock).mockResolvedValue({
        id: 1,
        theme: 'communication',
        skills: ['active listening', 'feedback']
      });
      (ConversationService.getConversationMessages as jest.Mock).mockResolvedValue([
        { character: 'user', text: 'Hello' },
        { character: 'Fora', text: 'Hi there!' },
        { character: 'user', text: 'How do I give feedback?' }
      ]);
      (ConversationWorkflowService as any).getMessageCount = jest.fn().mockResolvedValue(3);
      (PromptService.getSystemPrompt as jest.Mock).mockResolvedValue('system prompt');

      mockLLMService.generate.mockResolvedValue({
        reply: [{ character: 'Fora', text: 'Great question!', delay: 2000 }],
        theme: 'communication',
        skills: ['feedback']
      });
    });

    it('should include theme, skills, and conversation history in context for existing conversation', async () => {
      const result = await ConversationWorkflowService.chatWorkflow('How do I improve?', 1);

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation theme: communication')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation skills: active listening, feedback')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Full conversation history:')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('user: Hello')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Fora: Hi there!')
      );
    });

    it('should not include context for new conversation', async () => {
      const result = await ConversationWorkflowService.chatWorkflow('Hello', undefined);

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        'Hello'
      );
    });

    it('should handle conversation with string skills format', async () => {
      (ConversationService.getConversation as jest.Mock).mockResolvedValue({
        id: 1,
        theme: 'communication',
        skills: '["active listening", "feedback"]'
      });

      const result = await ConversationWorkflowService.chatWorkflow('How do I improve?', 1);

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation skills: active listening, feedback')
      );
    });
  });

  describe('interruptedChatWorkflow', () => {
    beforeEach(() => {
      (ConversationService.createConversation as jest.Mock).mockResolvedValue({ id: 1 });
      (ConversationService.addMessage as jest.Mock).mockResolvedValue({ id: 1 });
      (ConversationService.getConversation as jest.Mock).mockResolvedValue({
        id: 1,
        theme: 'communication',
        skills: ['active listening', 'feedback']
      });
      (ConversationService.getConversationMessages as jest.Mock).mockResolvedValue([
        { character: 'user', text: 'Hello' },
        { character: 'Fora', text: 'Hi there!' }
      ]);
      (ConversationWorkflowService as any).getMessageCount = jest.fn().mockResolvedValue(2);
      (PromptService.getSystemPrompt as jest.Mock).mockResolvedValue('system prompt');

      mockLLMService.generate.mockResolvedValue({
        reply: [{ character: 'Fora', text: 'I understand!', delay: 1000 }],
        theme: 'communication',
        skills: ['feedback']
      });
    });

    it('should include theme, skills, conversation history, and interrupted messages in context', async () => {
      const previousMessages = [
        { character: 'Jan', text: 'Let me think about this...' },
        { character: 'Lou', text: 'Actually, I have an idea!' }
      ];

      const result = await ConversationWorkflowService.interruptedChatWorkflow(
        'Wait, I have a question!',
        previousMessages,
        1
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation theme: communication')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation skills: active listening, feedback')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Full conversation history:')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Messages being processed when interrupted:')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Jan: Let me think about this...')
      );
    });
  });
});
