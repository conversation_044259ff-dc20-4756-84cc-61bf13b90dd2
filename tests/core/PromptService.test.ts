// Unmock PromptService for this test
jest.unmock('../../src/core/PromptService');

import { PromptService } from '../../src/core/PromptService';
import * as fs from 'fs/promises';
import * as path from 'path';

// Mock fs module
jest.mock('fs/promises');
const mockFs = fs as jest.Mocked<typeof fs>;

describe('PromptService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    PromptService.clearCache();
  });

  describe('getSystemPrompt', () => {
    it('should read and return a simple prompt without links', async () => {
      const mockContent = 'This is a simple prompt without any links.';
      mockFs.readFile.mockResolvedValue(mockContent);

      const result = await PromptService.getSystemPrompt('simple_prompt');

      expect(result).toBe(mockContent);
      expect(mockFs.readFile).toHaveBeenCalledWith(
        expect.stringContaining('prompts/simple_prompt.md'),
        'utf-8'
      );
    });

    it('should resolve embedded markdown links', async () => {
      const mainContent = `Main prompt content.

[Character Directions](character_system.md)

More content here.

[Skills List](skills_system.md)`;

      const characterContent = 'Character system content here.';
      const skillsContent = 'Skills system content here.';

      // Mock file reads
      mockFs.readFile
        .mockResolvedValueOnce(mainContent) // Main file
        .mockResolvedValueOnce(characterContent) // character_system.md
        .mockResolvedValueOnce(skillsContent); // skills_system.md

      const result = await PromptService.getSystemPrompt('agent_system');

      // Verify the links were resolved
      expect(result).toContain('### Character Directions');
      expect(result).toContain('Character system content here.');
      expect(result).toContain('### Skills List');
      expect(result).toContain('Skills system content here.');
      expect(result).not.toContain('[Character Directions](character_system.md)');
      expect(result).not.toContain('[Skills List](skills_system.md)');

      // Verify all files were read
      expect(mockFs.readFile).toHaveBeenCalledTimes(3);
    });

    it('should handle missing referenced files gracefully', async () => {
      const mainContent = `Main prompt content.

[Missing File](missing_file.md)

More content here.`;

      // Mock main file read success, referenced file read failure
      mockFs.readFile
        .mockResolvedValueOnce(mainContent) // Main file
        .mockRejectedValueOnce(new Error('File not found')); // missing_file.md

      const result = await PromptService.getSystemPrompt('agent_system');

      // Should keep the original link when file can't be resolved
      expect(result).toContain('[Missing File](missing_file.md)');
      expect(result).toContain('Main prompt content.');
      expect(result).toContain('More content here.');
    });

    it('should cache resolved prompts', async () => {
      const mockContent = 'Cached prompt content.';
      mockFs.readFile.mockResolvedValue(mockContent);

      // First call
      const result1 = await PromptService.getSystemPrompt('cached_prompt');
      // Second call
      const result2 = await PromptService.getSystemPrompt('cached_prompt');

      expect(result1).toBe(mockContent);
      expect(result2).toBe(mockContent);
      // File should only be read once due to caching
      expect(mockFs.readFile).toHaveBeenCalledTimes(1);
    });

    it('should handle complex nested links', async () => {
      const mainContent = `# Main Prompt

[Section A](section_a.md)

Some text.

[Section B](section_b.md)`;

      const sectionAContent = `Section A content.

[Nested Reference](nested.md)`;

      const sectionBContent = 'Section B content.';
      const nestedContent = 'Nested content.';

      mockFs.readFile
        .mockResolvedValueOnce(mainContent) // Main file
        .mockResolvedValueOnce(sectionAContent) // section_a.md
        .mockResolvedValueOnce(sectionBContent) // section_b.md
        .mockResolvedValueOnce(nestedContent); // nested.md (from section A)

      const result = await PromptService.getSystemPrompt('complex_prompt');

      expect(result).toContain('### Section A');
      expect(result).toContain('Section A content.');
      expect(result).toContain('### Section B');
      expect(result).toContain('Section B content.');
      expect(result).toContain('### Nested Reference');
      expect(result).toContain('Nested content.');
    });

    it('should throw error when main prompt file is not found', async () => {
      mockFs.readFile.mockReset();
      mockFs.readFile.mockRejectedValue(new Error('File not found'));

      await expect(PromptService.getSystemPrompt('nonexistent'))
        .rejects.toThrow('Prompt file not found: nonexistent');
    });
  });

  describe('cache management', () => {
    it('should clear cache', () => {
      PromptService.clearCache();
      expect(PromptService.getCacheSize()).toBe(0);
    });

    it('should track cache size', async () => {
      mockFs.readFile.mockResolvedValue('test content');

      expect(PromptService.getCacheSize()).toBe(0);
      
      await PromptService.getSystemPrompt('test1');
      expect(PromptService.getCacheSize()).toBe(1);
      
      await PromptService.getSystemPrompt('test2');
      expect(PromptService.getCacheSize()).toBe(2);
    });
  });
});
