import { DB<PERSON> } from '@dbos-inc/dbos-sdk';
import { SessionService } from '../src/core/SessionService';
import { ConversationService } from '../src/core/ConversationService';
import { SessionCleanupService } from '../src/core/SessionCleanupService';
import { ForaChat } from '../src/operations';
import { Session, SessionCreateRequest, Conversation } from '../src/models/types';

// Services are already mocked in setup.ts
const mockSessionService = SessionService as jest.Mocked<typeof SessionService>;
const mockConversationService = ConversationService as jest.Mocked<typeof ConversationService>;
const mockSessionCleanupService = SessionCleanupService as jest.Mocked<typeof SessionCleanupService>;

// Helper functions to create properly typed mock objects
const createMockSession = (overrides: Partial<Session> = {}): Session => ({
  id: 'mock-session-id',
  user_identifier: 'mock-user',
  channel: 'web',
  created_at: new Date(),
  updated_at: new Date(),
  last_activity: new Date(),
  expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
  metadata: {},
  conversation_id: undefined,
  ...overrides
});

const createMockConversation = (overrides: Partial<Conversation> = {}): Conversation => ({
  id: 1,
  created_at: new Date(),
  updated_at: new Date(),
  theme: undefined,
  skills: undefined,
  last_user_activity: new Date(),
  engagement_level: 1.0,
  ...overrides
});

describe('Session Management', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Reset all mock implementations to avoid cross-test contamination
    mockSessionService.createSession.mockReset();
    mockSessionService.getSession.mockReset();
    mockSessionService.getSessionByUserAndChannel.mockReset();
    mockSessionService.updateSessionActivity.mockReset();
    mockSessionService.updateSessionConversation.mockReset();
    mockSessionService.deleteSession.mockReset();
    mockSessionService.cleanupExpiredSessions.mockReset();
    mockSessionService.extendSession.mockReset();

    mockConversationService.createConversation.mockReset();
    mockConversationService.createConversationForSession.mockReset();
    mockConversationService.getConversationBySession.mockReset();
    mockConversationService.getSessionsForConversation.mockReset();
    mockConversationService.getActiveSessionsForUser.mockReset();
    mockConversationService.addMessage.mockReset();

    mockSessionCleanupService.getCleanupStats.mockReset();
    mockSessionCleanupService.manualCleanup.mockReset();
  });

  describe('SessionService', () => {
    test('should create a new session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_123',
        channel: 'web',
        metadata: { test: 'data' }
      };

      // Configure mock to return specific values for this test
      mockSessionService.createSession.mockResolvedValue(createMockSession({
        id: 'test-session-123',
        user_identifier: 'test_user_123',
        channel: 'web',
        metadata: { test: 'data' }
      }));

      const session = await SessionService.createSession(sessionRequest);

      expect(session).toBeDefined();
      expect(session.id).toBeDefined();
      expect(session.user_identifier).toBe('test_user_123');
      expect(session.channel).toBe('web');
      expect(session.metadata).toEqual({ test: 'data' });
      expect(session.expires_at).toBeDefined();
      expect(mockSessionService.createSession).toHaveBeenCalledWith(sessionRequest);
    });

    test('should get session by ID', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_456',
        channel: 'repl'
      };

      const mockSession = createMockSession({
        id: 'test-session-456',
        user_identifier: 'test_user_456',
        channel: 'repl'
      });

      mockSessionService.createSession.mockResolvedValue(mockSession);
      mockSessionService.getSession.mockResolvedValue(mockSession);

      const createdSession = await SessionService.createSession(sessionRequest);
      const retrievedSession = await SessionService.getSession(createdSession.id);

      expect(retrievedSession).toBeDefined();
      expect(retrievedSession!.id).toBe(createdSession.id);
      expect(retrievedSession!.user_identifier).toBe('test_user_456');
      expect(retrievedSession!.channel).toBe('repl');
      expect(mockSessionService.getSession).toHaveBeenCalledWith(createdSession.id);
    });

    test('should get session by user and channel', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_789',
        channel: 'sms'
      };

      const mockSession = createMockSession({
        id: 'test-session-789',
        user_identifier: 'test_user_789',
        channel: 'sms'
      });

      mockSessionService.createSession.mockResolvedValue(mockSession);
      mockSessionService.getSessionByUserAndChannel.mockResolvedValue(mockSession);

      const createdSession = await SessionService.createSession(sessionRequest);
      const retrievedSession = await SessionService.getSessionByUserAndChannel('test_user_789', 'sms');

      expect(retrievedSession).toBeDefined();
      expect(retrievedSession!.id).toBe(createdSession.id);
      expect(mockSessionService.getSessionByUserAndChannel).toHaveBeenCalledWith('test_user_789', 'sms');
    });

    test('should update session activity', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_activity',
        channel: 'web'
      };

      const originalActivity = new Date(Date.now() - 5000); // 5 seconds ago
      const updatedActivity = new Date(Date.now() + 1000); // 1 second in the future

      const originalSession = createMockSession({
        id: 'test-session-activity',
        user_identifier: 'test_user_activity',
        channel: 'web',
        last_activity: originalActivity
      });

      const updatedSession = createMockSession({
        ...originalSession,
        last_activity: updatedActivity
      });

      mockSessionService.createSession.mockResolvedValue(originalSession);
      // After updating activity, getSession should return updated session
      mockSessionService.getSession.mockResolvedValue(updatedSession);

      const session = await SessionService.createSession(sessionRequest);
      await SessionService.updateSessionActivity(session.id);
      const retrievedSession = await SessionService.getSession(session.id);

      expect(mockSessionService.updateSessionActivity).toHaveBeenCalledWith(session.id);
      expect(retrievedSession!.last_activity.getTime()).toBeGreaterThan(originalActivity.getTime());
    });

    test('should update session conversation', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_conv',
        channel: 'web'
      };

      const mockSession = createMockSession({
        id: 'test-session-conv',
        user_identifier: 'test_user_conv',
        channel: 'web'
      });

      const mockConversation = createMockConversation({
        id: 2
      });

      const updatedSession = createMockSession({
        ...mockSession,
        conversation_id: mockConversation.id
      });

      mockSessionService.createSession.mockResolvedValue(mockSession);
      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      // After updating conversation, getSession should return updated session
      mockSessionService.getSession.mockResolvedValue(updatedSession);

      const session = await SessionService.createSession(sessionRequest);
      const conversation = await ConversationService.createConversation();

      await SessionService.updateSessionConversation(session.id, conversation.id);
      const retrievedSession = await SessionService.getSession(session.id);

      expect(mockSessionService.updateSessionConversation).toHaveBeenCalledWith(session.id, conversation.id);
      expect(retrievedSession!.conversation_id).toBe(conversation.id);
    });

    test('should delete session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_delete',
        channel: 'web'
      };

      const mockSession = createMockSession({
        id: 'test-session-delete',
        user_identifier: 'test_user_delete',
        channel: 'web'
      });

      mockSessionService.createSession.mockResolvedValue(mockSession);
      // After deletion, getSession should return null
      mockSessionService.getSession.mockResolvedValue(null);

      const session = await SessionService.createSession(sessionRequest);
      await SessionService.deleteSession(session.id);
      const deletedSession = await SessionService.getSession(session.id);

      expect(mockSessionService.deleteSession).toHaveBeenCalledWith(session.id);
      expect(deletedSession).toBeNull();
    });

    test('should cleanup expired sessions', async () => {
      // Create an expired session
      const expiredSessionRequest: SessionCreateRequest = {
        userIdentifier: 'expired_user',
        channel: 'web',
        expiresAt: new Date(Date.now() - 1000) // Expired 1 second ago
      };

      // Create a valid session
      const validSessionRequest: SessionCreateRequest = {
        userIdentifier: 'valid_user',
        channel: 'web'
      };

      const validSession = createMockSession({
        id: 'valid-session',
        user_identifier: 'valid_user',
        channel: 'web'
      });

      mockSessionService.cleanupExpiredSessions.mockResolvedValue(1);
      mockSessionService.getSessionByUserAndChannel
        .mockResolvedValueOnce(validSession) // valid_user session exists
        .mockResolvedValueOnce(null); // expired_user session doesn't exist

      await SessionService.createSession(expiredSessionRequest);
      await SessionService.createSession(validSessionRequest);

      const deletedCount = await SessionService.cleanupExpiredSessions();

      expect(deletedCount).toBe(1);
      expect(mockSessionService.cleanupExpiredSessions).toHaveBeenCalled();

      // Verify only the valid session remains
      const retrievedValidSession = await SessionService.getSessionByUserAndChannel('valid_user', 'web');
      const retrievedExpiredSession = await SessionService.getSessionByUserAndChannel('expired_user', 'web');

      expect(retrievedValidSession).toBeDefined();
      expect(retrievedExpiredSession).toBeNull();
    });

    test('should extend session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_extend',
        channel: 'web'
      };

      const originalExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
      const extendedExpiry = new Date(Date.now() + 72 * 60 * 60 * 1000); // 72 hours from now (48 hours extension)

      const originalSession = createMockSession({
        id: 'test-session-extend',
        user_identifier: 'test_user_extend',
        channel: 'web',
        expires_at: originalExpiry
      });

      const extendedSession = createMockSession({
        ...originalSession,
        expires_at: extendedExpiry
      });

      mockSessionService.createSession.mockResolvedValue(originalSession);
      // After extending session, getSession should return extended session
      mockSessionService.getSession.mockResolvedValue(extendedSession);

      const session = await SessionService.createSession(sessionRequest);
      await SessionService.extendSession(session.id, 48); // Extend by 48 hours
      const retrievedSession = await SessionService.getSession(session.id);

      expect(mockSessionService.extendSession).toHaveBeenCalledWith(session.id, 48);
      expect(retrievedSession!.expires_at!.getTime()).toBeGreaterThan(originalExpiry.getTime());
    });
  });

  describe('Session-Conversation Integration', () => {
    test('should create conversation for session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_conv_create',
        channel: 'web'
      };

      const mockSession = createMockSession({
        id: 'test-session-conv-create',
        user_identifier: 'test_user_conv_create',
        channel: 'web'
      });

      const mockConversation = createMockConversation({
        id: 3
      });

      const updatedSession = createMockSession({
        ...mockSession,
        conversation_id: mockConversation.id
      });

      mockSessionService.createSession.mockResolvedValue(mockSession);
      mockConversationService.createConversationForSession.mockResolvedValue(mockConversation);
      // After creating conversation for session, getSession should return updated session
      mockSessionService.getSession.mockResolvedValue(updatedSession);

      const session = await SessionService.createSession(sessionRequest);
      const conversation = await ConversationService.createConversationForSession(session.id);

      expect(conversation).toBeDefined();
      expect(conversation.id).toBeDefined();
      expect(mockConversationService.createConversationForSession).toHaveBeenCalledWith(session.id);

      // Verify session was updated with conversation ID
      const retrievedSession = await SessionService.getSession(session.id);
      expect(retrievedSession!.conversation_id).toBe(conversation.id);
    });

    test('should get conversation by session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_get_conv',
        channel: 'web'
      };

      const mockSession = createMockSession({
        id: 'test-session-get-conv',
        user_identifier: 'test_user_get_conv',
        channel: 'web',
        conversation_id: 4
      });

      const mockConversation = createMockConversation({
        id: 4
      });

      mockSessionService.createSession.mockResolvedValue(mockSession);
      mockConversationService.createConversationForSession.mockResolvedValue(mockConversation);
      mockConversationService.getConversationBySession.mockResolvedValue(mockConversation);

      const session = await SessionService.createSession(sessionRequest);
      const conversation = await ConversationService.createConversationForSession(session.id);

      const retrievedConversation = await ConversationService.getConversationBySession(session.id);

      expect(retrievedConversation).toBeDefined();
      expect(retrievedConversation!.id).toBe(conversation.id);
      expect(mockConversationService.getConversationBySession).toHaveBeenCalledWith(session.id);
    });

    test('should get sessions for conversation', async () => {
      const mockConversation = createMockConversation({
        id: 5
      });

      const mockSession1 = createMockSession({
        id: 'session-1',
        user_identifier: 'user1',
        channel: 'web',
        conversation_id: mockConversation.id
      });

      const mockSession2 = createMockSession({
        id: 'session-2',
        user_identifier: 'user2',
        channel: 'repl',
        conversation_id: mockConversation.id
      });

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.getSessionsForConversation.mockResolvedValue([mockSession1, mockSession2]);

      const conversation = await ConversationService.createConversation();

      // Create multiple sessions for the same conversation
      const session1Request: SessionCreateRequest = {
        userIdentifier: 'user1',
        channel: 'web',
        conversationId: conversation.id
      };

      const session2Request: SessionCreateRequest = {
        userIdentifier: 'user2',
        channel: 'repl',
        conversationId: conversation.id
      };

      await SessionService.createSession(session1Request);
      await SessionService.createSession(session2Request);

      const sessions = await ConversationService.getSessionsForConversation(conversation.id);

      expect(sessions).toHaveLength(2);
      expect(sessions.map(s => s.user_identifier)).toContain('user1');
      expect(sessions.map(s => s.user_identifier)).toContain('user2');
      expect(mockConversationService.getSessionsForConversation).toHaveBeenCalledWith(conversation.id);
    });

    test('should get active sessions for user', async () => {
      const userIdentifier = 'multi_session_user';

      const mockWebSession = createMockSession({
        id: 'web-session',
        user_identifier: userIdentifier,
        channel: 'web',
        conversation_id: 6
      });

      const mockReplSession = createMockSession({
        id: 'repl-session',
        user_identifier: userIdentifier,
        channel: 'repl',
        conversation_id: 7
      });

      const mockConversation1 = createMockConversation({
        id: 6
      });

      const mockConversation2 = createMockConversation({
        id: 7
      });

      mockSessionService.createSession
        .mockResolvedValueOnce(mockWebSession)
        .mockResolvedValueOnce(mockReplSession);
      mockConversationService.createConversation
        .mockResolvedValueOnce(mockConversation1)
        .mockResolvedValueOnce(mockConversation2);
      mockConversationService.getActiveSessionsForUser.mockResolvedValue([mockWebSession, mockReplSession]);

      // Create multiple sessions for the same user
      const webSessionRequest: SessionCreateRequest = {
        userIdentifier,
        channel: 'web'
      };

      const replSessionRequest: SessionCreateRequest = {
        userIdentifier,
        channel: 'repl'
      };

      const webSession = await SessionService.createSession(webSessionRequest);
      const replSession = await SessionService.createSession(replSessionRequest);

      // Add conversations to make them "active"
      const conversation1 = await ConversationService.createConversation();
      const conversation2 = await ConversationService.createConversation();

      await SessionService.updateSessionConversation(webSession.id, conversation1.id);
      await SessionService.updateSessionConversation(replSession.id, conversation2.id);

      const activeSessions = await ConversationService.getActiveSessionsForUser(userIdentifier);

      expect(activeSessions).toHaveLength(2);
      expect(activeSessions.map(s => s.channel)).toContain('web');
      expect(activeSessions.map(s => s.channel)).toContain('repl');
      expect(mockConversationService.getActiveSessionsForUser).toHaveBeenCalledWith(userIdentifier);
    });
  });

  describe('Session Cleanup', () => {
    test('should get cleanup stats', async () => {
      // Create test data
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'stats_user',
        channel: 'web'
      };

      const mockStats = {
        totalSessions: 1,
        activeSessions: 1,
        totalConversations: 1,
        totalMessages: 1,
        expiredSessions: 0,
        orphanedConversations: 0,
        oldMessages: 0
      };

      const mockConversation = createMockConversation({ id: 8 });

      mockSessionCleanupService.getCleanupStats.mockResolvedValue(mockStats);
      mockConversationService.createConversation.mockResolvedValue(mockConversation);

      await SessionService.createSession(sessionRequest);
      const conversation = await ConversationService.createConversation();
      await ConversationService.addMessage('user', 'test message', conversation.id);

      const stats = await SessionCleanupService.getCleanupStats();

      expect(stats.totalSessions).toBeGreaterThan(0);
      expect(stats.totalConversations).toBeGreaterThan(0);
      expect(stats.totalMessages).toBeGreaterThan(0);
      expect(mockSessionCleanupService.getCleanupStats).toHaveBeenCalled();
    });

    test('should perform manual cleanup', async () => {
      // Create expired session
      const expiredSessionRequest: SessionCreateRequest = {
        userIdentifier: 'cleanup_user',
        channel: 'web',
        expiresAt: new Date(Date.now() - 1000)
      };

      const mockCleanupResult = {
        expiredSessions: 1,
        orphanedConversations: 0,
        oldMessages: 0,
        messageQueueEntries: 0
      };

      mockSessionCleanupService.manualCleanup.mockResolvedValue(mockCleanupResult);

      await SessionService.createSession(expiredSessionRequest);

      const result = await SessionCleanupService.manualCleanup({
        cleanupExpiredSessions: true,
        cleanupOrphanedConversations: false,
        cleanupOldMessages: false,
        cleanupMessageQueue: false
      });

      expect(result.expiredSessions).toBe(1);
      expect(mockSessionCleanupService.manualCleanup).toHaveBeenCalledWith({
        cleanupExpiredSessions: true,
        cleanupOrphanedConversations: false,
        cleanupOldMessages: false,
        cleanupMessageQueue: false
      });
    });
  });

  describe('ForaChat Operations Integration', () => {
    test('should create session through ForaChat operations', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'forachat_user',
        channel: 'web'
      };

      const mockSession = createMockSession({
        id: 'forachat-session-id',
        user_identifier: 'forachat_user',
        channel: 'web'
      });

      // Mock the DBOS workflow
      const mockHandle = {
        getResult: jest.fn().mockResolvedValue(mockSession)
      };

      (DBOS.startWorkflow as jest.Mock).mockReturnValue({
        createSession: jest.fn().mockResolvedValue(mockHandle)
      });

      const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
      const session = await handle.getResult();

      expect(session).toBeDefined();
      expect(session.user_identifier).toBe('forachat_user');
    });

    test('should get session through ForaChat operations', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'forachat_get_user',
        channel: 'web'
      };

      const mockSession = createMockSession({
        id: 'forachat-get-session-id',
        user_identifier: 'forachat_get_user',
        channel: 'web'
      });

      const mockCreateHandle = {
        getResult: jest.fn().mockResolvedValue(mockSession)
      };

      const mockGetHandle = {
        getResult: jest.fn().mockResolvedValue(mockSession)
      };

      (DBOS.startWorkflow as jest.Mock).mockReturnValue({
        createSession: jest.fn().mockResolvedValue(mockCreateHandle),
        getSession: jest.fn().mockResolvedValue(mockGetHandle)
      });

      const createHandle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
      const createdSession = await createHandle.getResult();

      const getHandle = await DBOS.startWorkflow(ForaChat).getSession(createdSession.id);
      const retrievedSession = await getHandle.getResult();

      expect(retrievedSession).toBeDefined();
      expect(retrievedSession!.id).toBe(createdSession.id);
    });

    test('should cleanup sessions through ForaChat operations', async () => {
      const expiredSessionRequest: SessionCreateRequest = {
        userIdentifier: 'forachat_cleanup_user',
        channel: 'web',
        expiresAt: new Date(Date.now() - 1000)
      };

      const mockSession = createMockSession({
        id: 'forachat-cleanup-session-id',
        user_identifier: 'forachat_cleanup_user',
        channel: 'web',
        expires_at: new Date(Date.now() - 1000)
      });

      const mockCreateHandle = {
        getResult: jest.fn().mockResolvedValue(mockSession)
      };

      const mockCleanupHandle = {
        getResult: jest.fn().mockResolvedValue(1)
      };

      (DBOS.startWorkflow as jest.Mock).mockReturnValue({
        createSession: jest.fn().mockResolvedValue(mockCreateHandle),
        cleanupExpiredSessions: jest.fn().mockResolvedValue(mockCleanupHandle)
      });

      const createHandle = await DBOS.startWorkflow(ForaChat).createSession(expiredSessionRequest);
      await createHandle.getResult();

      const cleanupHandle = await DBOS.startWorkflow(ForaChat).cleanupExpiredSessions();
      const deletedCount = await cleanupHandle.getResult();

      expect(deletedCount).toBe(1);
    });
  });
});
