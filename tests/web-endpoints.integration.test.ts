import request from 'supertest';
import { ForaChatApp } from '../src/ForaChatApp';

// This file contains integration tests that test the full HTTP flow
// These tests use the actual ForaChatApp with mocked services
// They test the complete request/response cycle including middleware

describe('Web Endpoints Integration Tests', () => {
  let app: ForaChatApp;
  let server: any;

  beforeAll(async () => {
    app = new ForaChatApp();
    await app.initialize();
    server = app.getExpressApp();
  });

  afterAll(async () => {
    if (app) {
      await app.shutdown();
    }
  });

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('Session Middleware Integration', () => {
    test('should create session cookie on first chat request', async () => {
      const response = await request(server)
        .post('/chat')
        .send({ text: 'Hello' })
        .expect(200);

      expect(response.body.reply).toBeDefined();
      expect(response.body.conversationId).toBeDefined();
      
      // Check that a session cookie was set
      const cookies = response.headers['set-cookie'];
      expect(cookies).toBeDefined();
      
      const sessionCookie = Array.isArray(cookies) 
        ? cookies.find((cookie: string) => cookie.startsWith('forachat_session='))
        : cookies;
      expect(sessionCookie).toBeDefined();
    });

    test('should restore session from cookie on subsequent requests', async () => {
      // First request to create session
      const firstResponse = await request(server)
        .post('/chat')
        .send({ text: 'First message' })
        .expect(200);

      const cookies = firstResponse.headers['set-cookie'];
      const sessionCookie = Array.isArray(cookies) 
        ? cookies.find((cookie: string) => cookie.startsWith('forachat_session='))
        : cookies;

      // Second request with session cookie
      const secondResponse = await request(server)
        .post('/chat')
        .set('Cookie', sessionCookie!)
        .send({ text: 'Second message' })
        .expect(200);

      expect(secondResponse.body.reply).toBeDefined();
      
      // Should not set a new cookie since session exists
      const secondCookies = secondResponse.headers['set-cookie'];
      expect(secondCookies).toBeUndefined();
    });
  });

  describe('Chat Endpoint Integration', () => {
    test('should handle chat requests and return structured response', async () => {
      const response = await request(server)
        .post('/chat')
        .send({ text: 'Tell me about effective communication' })
        .expect(200);

      expect(response.body).toHaveProperty('reply');
      expect(response.body).toHaveProperty('conversationId');
      expect(response.body).toHaveProperty('skills');
      expect(response.body).toHaveProperty('theme');
      
      expect(Array.isArray(response.body.reply)).toBe(true);
      expect(response.body.reply.length).toBeGreaterThan(0);
      
      // Check reply structure
      const firstReply = response.body.reply[0];
      expect(firstReply).toHaveProperty('character');
      expect(firstReply).toHaveProperty('text');
      expect(firstReply).toHaveProperty('delay');
    });

    test('should return 400 for empty text', async () => {
      const response = await request(server)
        .post('/chat')
        .send({ text: '' })
        .expect(400);

      expect(response.body.error).toContain('non-empty "text" field');
    });

    test('should return 400 for missing text', async () => {
      const response = await request(server)
        .post('/chat')
        .send({})
        .expect(400);

      expect(response.body.error).toContain('non-empty "text" field');
    });
  });

  describe('Conversation History Integration', () => {
    test('should retrieve conversation history', async () => {
      // First, create a conversation
      const chatResponse = await request(server)
        .post('/chat')
        .send({ text: 'Hello, how are you?' })
        .expect(200);

      const conversationId = chatResponse.body.conversationId;

      // Then retrieve the conversation history
      const historyResponse = await request(server)
        .get(`/conversation/${conversationId}`)
        .expect(200);

      expect(historyResponse.body).toHaveProperty('conversationId');
      expect(historyResponse.body).toHaveProperty('messages');
      expect(historyResponse.body.conversationId).toBe(conversationId);
      expect(Array.isArray(historyResponse.body.messages)).toBe(true);
    });

    test('should return 400 for invalid conversation ID', async () => {
      const response = await request(server)
        .get('/conversation/invalid')
        .expect(400);

      expect(response.body.error).toBe('Invalid conversation ID');
    });

    test('should continue conversation with existing ID', async () => {
      // First chat to create conversation
      const firstResponse = await request(server)
        .post('/chat')
        .send({ text: 'Hello' })
        .expect(200);

      const conversationId = firstResponse.body.conversationId;

      // Continue the conversation
      const continueResponse = await request(server)
        .post(`/conversation/${conversationId}`)
        .send({ text: 'Tell me more' })
        .expect(200);

      expect(continueResponse.body).toHaveProperty('reply');
      expect(continueResponse.body).toHaveProperty('conversationId');
      expect(continueResponse.body.conversationId).toBe(conversationId);
    });
  });

  describe('Session Conversations Integration', () => {
    test('should get session conversations', async () => {
      // Create a session with conversation
      const chatResponse = await request(server)
        .post('/chat')
        .send({ text: 'Hello' })
        .expect(200);

      const cookies = chatResponse.headers['set-cookie'];
      const sessionCookie = Array.isArray(cookies) 
        ? cookies.find((cookie: string) => cookie.startsWith('forachat_session='))
        : cookies;

      // Get session conversations
      const response = await request(server)
        .get('/session/conversations')
        .set('Cookie', sessionCookie!)
        .expect(200);

      expect(response.body).toHaveProperty('conversationId');
      expect(response.body).toHaveProperty('messages');
    });

    test('should return empty conversations for session without conversation', async () => {
      // Create a session without conversation by directly accessing the endpoint
      const response = await request(server)
        .get('/session/conversations')
        .expect(200);

      expect(response.body).toHaveProperty('conversations');
      expect(response.body.conversations).toEqual([]);
    });
  });

  describe('Error Handling Integration', () => {
    test('should handle internal server errors gracefully', async () => {
      // This test would require mocking a service to throw an error
      // For now, we'll test a scenario that might cause an error
      const response = await request(server)
        .post('/conversation/999999')
        .send({ text: 'Test message' })
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('An internal server error occurred.');
    });

    test('should handle malformed JSON gracefully', async () => {
      const response = await request(server)
        .post('/chat')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);

      // Express should handle malformed JSON and return 400
    });
  });

  describe('Health Check Integration', () => {
    test('should respond to health check', async () => {
      const response = await request(server)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body.status).toBe('ok');
    });
  });
});
